<template>
  <el-space wrap style="width: 100%" fill>
    <div class="box p-6 mb-4 rounded-3">
      <el-row :gutter="24">
        <el-col v-for="item in lists" :key="item.name" :xs="20" :sm="10" :md="8" :lg="8" :xl="6" :xxl="6">
          <div class="container-fluid py-5" style="height: 600px">
            <el-skeleton style=" display: flex; gap: 8px;" :loading="loading" animated :count="6">
              <template #template>
                <div style="flex: 1">
                  <el-skeleton-item variant="image" style="height: 240px" />
                  <div style="padding: 14px">
                    <el-skeleton-item variant="h3" style="width: 50%" />
                    <div style="display: flex;align-items: center;  justify-items: space-between; margin-top: 16px;height: 16px;">
                      <el-skeleton-item variant="text" style="margin-right: 16px" />
                      <el-skeleton-item variant="text" style="width: 30%" />
                    </div>
                  </div>
                </div>
              </template>
              <template #default>
                <el-card :body-style="{ padding: '0px', marginBottom: '1px',height:'100%' }" class="rounded-3">
                  <!-- <img :src="item.imgUrl" class="image multi-content" fit="cover" style="width:100%; height:auto; display:block; object-fit:contain;" /> -->
                  <div @click="goShopEvent()" class="modelImages" style="display: flex; justify-content: center; align-items: center; height: 500px; width: 100%;background-color: #efefef;cursor: pointer;">
                    <img :src="item.imgUrl" class="image multi-content" style="max-height: 100%; max-width: 100%; object-fit: contain;" />
                  </div>
                  <div style="padding: 14px">
                    <span>{{ item.name }}</span>
                    <div class="bottom card-header">
                      <div class="time">
                        <el-row>
                          <el-col :span="20">
                            {{ currentDate }}
                          </el-col>
                          <el-col :span="4" style="text-align: right;">
                            <el-button text class="button" @click="goShopEvent()" style="margin-left:auto;">
                              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-cart-plus" viewBox="0 0 16 16">
                                <path d="M9 5.5a.5.5 0 0 0-1 0V7H6.5a.5.5 0 0 0 0 1H8v1.5a.5.5 0 0 0 1 0V8h1.5a.5.5 0 0 0 0-1H9z" />
                                <path d="M.5 1a.5.5 0 0 0 0 1h1.11l.401 1.607 1.498 7.985A.5.5 0 0 0 4 12h1a2 2 0 1 0 0 4 2 2 0 0 0 0-4h7a2 2 0 1 0 0 4 2 2 0 0 0 0-4h1a.5.5 0 0 0 .491-.408l1.5-8A.5.5 0 0 0 14.5 3H2.89l-.405-1.621A.5.5 0 0 0 2 1zm3.915 10L3.102 4h10.796l-1.313 7zM6 14a1 1 0 1 1-2 0 1 1 0 0 1 2 0m7 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0" />
                              </svg></el-button>
                          </el-col>
                        </el-row>
                      </div>
                    </div>
                  </div>
                </el-card>
              </template>
            </el-skeleton>
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- <div>
      <el-button @click="setLoading">Click me to reload</el-button>
    </div> -->

  </el-space>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";
interface ListItem {
  imgUrl: string;
  name: string;
}

const loading = ref(true);
const lists = ref<ListItem[]>([]);
const currentDate = new Date().toDateString();
const router = useRouter();
const goShopEvent = function () {
  router.push({ path: "/custommade" });
};
const setLoading = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 2000);
};

onMounted(() => {
  loading.value = false;
  lists.value = [
    {
      imgUrl:
        "https://cdn.suitsupply.com/image/upload/b_rgb:efefef,bo_80px_solid_rgb:efefef,c_fill,w_2600,h_3597/b_rgb:efefef,c_pad,dpr_1,w_990,h_1369,f_auto,q_auto,fl_progressive/products/suits/default/Summer/P7070_104.jpg",
      name: "Deer",
    },
    {
      imgUrl:
        "https://cdn.suitsupply.com/image/upload/b_rgb:efefef,bo_80px_solid_rgb:efefef,c_fill,w_2600,h_3597/b_rgb:efefef,c_pad,dpr_1,w_990,h_1369,f_auto,q_auto,fl_progressive/products/suits/default/Winter/P6931_104.jpg",
      name: "Horse",
    },
    {
      imgUrl:
        "https://cdn.suitsupply.com/image/upload/ar_10:21,b_rgb:efefef,bo_200px_solid_rgb:efefef,c_pad,g_north,w_2600/b_rgb:efefef,c_lfill,g_north,dpr_1,h_1728,w_1440,f_auto,q_auto,fl_progressive/products/suits/default/Summer/P7033_1.jpg",
      name: "Mountain Lion",
    },
    {
      imgUrl:
        "https://cdn.suitsupply.com/image/upload/b_rgb:efefef,c_fill,w_2600/b_rgb:efefef,c_fill,dpr_1,w_1440,h_1728,f_auto,q_auto,fl_progressive/products/suits/default/Summer/P7033_101.jpg",
      name: "Deer",
    },
    {
      imgUrl:
        "https://cdn.suitsupply.com/image/upload/b_rgb:efefef,bo_80px_solid_rgb:efefef,c_fill,w_2600,h_3597/b_rgb:efefef,c_pad,dpr_1,w_990,h_1369,f_auto,q_auto,fl_progressive/products/suits/default/Summer/P7012_104.jpg",
      name: "Horse",
    },
    {
      imgUrl:
        "https://cdn.suitsupply.com/image/upload/ar_10:21,b_rgb:efefef,bo_200px_solid_rgb:efefef,c_pad,g_north,w_2600/b_rgb:efefef,c_lfill,g_north,dpr_1,h_1728,w_1440,f_auto,q_auto,fl_progressive/products/suits/default/Summer/P7003_1.jpg",
      name: "Mountain Lion",
    },
    {
      imgUrl:
        "https://cdn.suitsupply.com/image/upload/b_rgb:efefef,c_fill,w_2600/b_rgb:efefef,c_fill,dpr_1,w_1440,h_1728,f_auto,q_auto,fl_progressive/products/suits/default/Summer/P7003_101.jpg",
      name: "Deer",
    },
    {
      imgUrl:
        "https://cdn.suitsupply.com/image/upload/b_rgb:efefef,bo_80px_solid_rgb:efefef,c_fill,w_2600,h_3597/b_rgb:efefef,c_pad,dpr_1,w_990,h_1369,f_auto,q_auto,fl_progressive/products/suits/default/Summer/P7003_104.jpg",
      name: "Horse",
    },
    {
      imgUrl:
        "https://cdn.suitsupply.com/image/upload/ar_10:21,b_rgb:efefef,bo_200px_solid_rgb:efefef,c_pad,g_north,w_2600/b_rgb:efefef,c_lfill,g_north,dpr_1,h_2304,w_1920,f_auto,q_auto,fl_progressive/products/suits/default/Summer/P7002_1.jpg",
      name: "Mountain Lion",
    },
  ];
});
</script>
<style lang="scss">
.el-col {
  border-radius: 4px;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
}
</style>
