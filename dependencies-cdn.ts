interface CdnDependency {
  name: string;
  library: string;
  js: string;
  css: string;
}
//s4.zstatic.net
//cdnjs.cloudflare.com
//cdn.jsdelivr.net
//unpkg.com
const isProd = process.env.NODE_ENV === "production" || "development";

const dependencies: CdnDependency[] = [
  {
    name: "Vue",
    library: "Vue",
    js: isProd
      ? "https://cdn.jsdelivr.net/npm/vue@3.4.25/dist/vue.global.prod.js"
      : "https://cdn.jsdelivr.net/npm/vue@3.4.25/dist/vue.global.js",
    css: "",
  },
  {
    name: "lodash",
    library: "_",
    js: "https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.21/lodash.min.js",
    css: "",
  },
  {
    name: "js-cookie",
    library: "Cookies",
    js: "https://cdnjs.cloudflare.com/ajax/libs/js-cookie/3.0.1/js.cookie.min.js",
    css: "",
  },
  {
    name: "axios",
    library: "axios",
    js: "https://cdnjs.cloudflare.com/ajax/libs/axios/0.24.0/axios.min.js",
    css: "",
  },
  {
    name: "bootstrap",
    library: "bootstrap",
    js: "https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.1/js/bootstrap.min.js",
    css: "https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.1/css/bootstrap.min.css",
  },
  {
    name: "xe",
    library: "xe-utils",
    js: "https://cdn.jsdelivr.net/npm/xe-utils@3.7.5/dist/xe-utils.umd.min.js",
    css: "",
  },
  {
    name: "axios",
    library: "axios",
    js: "https://cdn.jsdelivr.net/npm/axios@0.24.0/dist/axios.min.js",
    css: "",
  },
  {
    name: "Vue-router",
    library: "vue-router",
    js: isProd
      ? "https://cdn.jsdelivr.net/npm/vue-router@4.3.2/dist/vue-router.global.prod.js"
      : "https://cdn.jsdelivr.net/npm/vue-router@4.3.2/dist/vue-router.global.js",
    css: "",
  },
];

export default dependencies;
