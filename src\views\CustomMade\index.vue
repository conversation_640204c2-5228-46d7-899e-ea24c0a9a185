<template>
  <div class="mb-4 box rounded-3" style="height: 100%;">

    <div class="container-fluid py-7" style="height: 100%;">
      <div style="height: 100%; box-shadow: var(--el-border-color-light) 0px 0px 10px">
        <el-splitter>
          <el-splitter-panel size="70%">
            <div class="modelimages">
              <ModelImage />
            </div>
          </el-splitter-panel>
          <el-splitter-panel :min="'25%'" :max="'50%'">
            <ModelGroupMain :loading="Loading" :is-data-ready="isDataReady" />
            <div class="groupSetting">
              <el-dropdown placement="top-start" @command="groupoSettingEevent" id="groupSetting">
                <el-icon>
                  <Menu />
                </el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-for="item in groupSettingData" :command="item.groupIDs" :divided="item.divided" :key="item.value">{{ item.label }}</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            <div class="tourSetting">
              <el-button @click="openTourEvent"> <i class="bi bi-question-circle"></i></el-button>
            </div>
          </el-splitter-panel>
        </el-splitter>
      </div>
    </div>
  </div>
  <Tour :open="tourOpen" :ref1="'groupSetting'" />
</template>

<script>
import ModelGroupMain from "./components/ModelGroups/index.vue";
import ModelImage from "./components/ModelImage.vue";
import { getGroupById, getGroupSetting } from "@/mixins/group.js";
import useCustomMadeStore from "@/store/custommade.js";
import XEUtils from "xe-utils";
import { Menu } from '@element-plus/icons-vue'
import Tour from './components/tour.vue'
export default {
  name: "CustomMadeMain",
  components: {
    ModelGroupMain,
    ModelImage, Menu,
    Tour
  },
  data() {
    const activeComponentsStore = useCustomMadeStore();
    return {
      groupSetting: '',
      tourOpen: false,
      modelID: null,
      groupID: null,
      activeComponentsStore: activeComponentsStore,
      Loading: false,
      isDataReady: false,
      api: {
        getModelElemListByGroup: "/mtmshop/customMade/getModelElemListByGroup",
        GetModelSizeColumn: '/mtmshop/customMade/GetModelSizeColumn',
        GetModelBodyList: '/mtmshop/customMade/GetModelBodyList',
      },
      groupSettingData: []
    }
  },
  computed: {
    // 创建一个计算属性，用于派生出需要被侦听的特定值
    watchedGroup() {
      const { groupID, modelID } = this.activeComponentsStore.activeGroup || {};
      return { groupID, modelID };
    },
    // groupSetting() {
    //   return getGroupSetting()
    // }
  },
  watch: {
    // 监听计算属性的变化
    watchedGroup: {
      handler(newGroup, oldGroup) {
        // 只有在 groupID 或 modelID 发生变化时才调用
        // oldGroup 可能在首次触发时为 undefined
        if (newGroup.modelID && newGroup.groupID) {
          var findGroupData = this.activeComponentsStore.groupData.find(item => item.value === newGroup.groupID)
          if (findGroupData && (findGroupData.sizeColumnData <= 0 || findGroupData.modelID != newGroup.modelID)) {
            this.loadDetailSizeColumnData(newGroup.groupID, newGroup.modelID);
          }
          if (findGroupData && (findGroupData.bodyListData <= 0 || findGroupData.modelID != newGroup.modelID)) {
            this.loadDetailBodyListData(newGroup.groupID, newGroup.modelID);
          }

        }
      },
      deep: true, // deep is still useful if the values could be objects
      immediate: true // 如果需要在组件加载时立即执行一次，可以添加此项
    }
  },
  created() {
    this.groupSettingData = getGroupSetting()
  },
  methods: {
    openTourEvent() {
      this.tourOpen = true
    },
    //获取所有品类的款式明细
    async extendDataWithDetails(data) {
      const res = await this.$api.ActionRequest(
        this.api.getModelElemListByGroup,
        {
          modelID: this.activeComponentsStore.activeGroup.modelID,
          groupID: this.activeComponentsStore.activeGroup.groupID,
        }
      );
      if (res) {
        data.forEach((item) => {
          const group = res.find((g) => g.groupID === item.value);
          if (group) {
            item.modelElemListGroup = group.modelElemListGroup;
            item.modelID = group.modelID;
            item.modelCode = group.modelCode;
            item.modelCodeName = group.modelCodeName;
            item.modelElemList = group.modelElemList;
          }
        });
      }
      return data;
    },
    async getModelElemListGroupData() {
      XEUtils.arrayEach(
        this.activeComponentsStore.groupData,
        async (item, key) => { }
      );
    },
    async initializeData() {
      try {
        this.Loading = true;
        const baseData = await this.activeComponentsStore.loadGroupData();
        if (baseData && baseData.length > 0) {
          // if (baseData[0].modelElemListGroup && baseData[0].modelElemListGroup.length > 0) {
          //   this.isDataReady = true;
          //   return;
          // }
          const extendedData = await this.extendDataWithDetails([...baseData]);
          this.activeComponentsStore.setGroupData(extendedData);
          this.isDataReady = true;
        }
      } catch (error) {
        console.error("❌ Failed to load data:", error);
      } finally {
        this.Loading = false;
      }
    },
    async loadDetailSizeColumnData(groupID, modelID) {
      if (modelID === null || modelID === '') {
        return;
      }
      await this.$api.ActionRequest(
        this.api.GetModelSizeColumn,
        {
          modelID: modelID,
          groupID: groupID,
        }
      ).then(res => {
        if (res) {
          this.activeComponentsStore.setSizeColumnDataByGroupID(groupID, res)
          // const group = this.activeComponentsStore.groupData.find(g => g.value === groupID);
          // if (group) {
          //   group.sizeColumnData = res;
          // }
        }
      }).catch(res => {

      })
    },
    async loadDetailBodyListData(groupID, modelID) {
      if (modelID === null || modelID === '') {
        return;
      }
      await this.$api.ActionRequest(
        this.api.GetModelBodyList,
        {
          modelID: modelID,
          groupID: groupID,
        }
      ).then(res => {
        if (res) {
          this.activeComponentsStore.setBodyListDataByGroupID(groupID, res)
          // const group = this.activeComponentsStore.groupData.find(g => g.value === groupID);
          // if (group) {
          //   group.bodyListData = res;
          // }
        }
      }).catch(res => {

      })
    },
    groupoSettingEevent(value) {
      this.activeComponentsStore.setActiveGroupTab(value)
    }
  },
  // Temporarily commented out to debug dynamic import issue
  beforeRouteEnter(to, from, next) {
    next(vm => {
      // 通过 history.state 获取 state 数据
      if (history.state) {
        const activeComponentsStore = useCustomMadeStore();
        vm.modelID = history.state.modelID;
        vm.groupID = history.state.groupID;
        var item = getGroupById(history.state.groupID)
        if (item) {
          activeComponentsStore.setActiveGroup(item.code, item.value, history.state.modelID, history.state.modelCode, history.state.modelCodeName);
          // 找到 groupData 中对应的项并更新 modelID, modelCode, modelCodeName
          // activeComponentsStore.groupData.forEach(model => {
          //   if (model.value === history.state.groupID) {
          //     model.modelID = history.state.modelID;
          //     model.modelCode = history.state.modelCode;
          //     model.modelCodeName = history.state.modelCodeName;
          //     model.isActive = true
          //   } else {
          //     model.isActive = false
          //   }
          // })
          activeComponentsStore.setActiveGroupTab([history.state.groupID])
          const targetGroup = activeComponentsStore.groupData.find(group => group.value === history.state.groupID);
          if (targetGroup) {
            targetGroup.modelID = history.state.modelID;
            targetGroup.modelCode = history.state.modelCode;
            targetGroup.modelCodeName = history.state.modelCodeName;
          }
        }

        console.log("在 beforeRouteEnter 中接收到的数据:", history.state);
        // 在设置完状态后调用数据初始化
        vm.initializeData();
      }
    })
  },
  beforeRouteLeave(to, from, next) {
    // 在这里添加用户离开页面时的逻辑
    // console.log('用户即将离开 CustomMade 页面');
    // 例如，可以清除一些状态或保存数据
    const customMadeStore = useCustomMadeStore();
    customMadeStore.setactiveComponentName('ModelGroupTabs')
    // customMadeStore.resetData(); // 假设你有一个重置数据的方法

    next(); // 确保导航继续
  },
  mounted() {
    // 调试检查
    // console.log('mounted - initializeData exists:', typeof this.initializeData);
    // console.log('mounted - methods:', Object.keys(this.$options.methods || {}));

    // 只有在没有通过路由状态初始化时才调用
    if (!history.state) {
      if (typeof this.initializeData === 'function') {
        this.initializeData();
      } else {
        console.error('initializeData method not found!');
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.modelimages {
  position: relative;
  height: 100%;
  display: flex;
  //justify-content: center;
  //align-items: center;
}
.groupSetting {
  position: absolute;
  top: 1.4rem;
  // left: 0.7rem;
  z-index: 9999;
}
.tourSetting {
  position: absolute;
  top: 1.4rem;
  right: 0.7rem;
  z-index: 9999;
}
</style>
