{"name": "<PERSON><PERSON><PERSON>", "version": "1.1.9", "description": "xia<PERSON> yun", "homepage": "", "license": "CC-BY-4.0", "repository": "", "author": "Sunday<<EMAIL>>", "keywords": ["vue 3", "bootstrap 5"], "scripts": {"dev": "vite", "build": "vite build --mode development", "build:dev": "vite build --mode staging", "build:production": "vite build --mode production", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@executeautomation/playwright-mcp-server": "^1.0.6", "@popperjs/core": "^2.11.6", "@vueuse/core": "^13.4.0", "axios": "^0.24.0", "bootstrap": "^5.3.1", "bootstrap-icons": "^1.10.3", "element-plus": "^2.10.1", "gsap": "^3.13.0", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "pinia": "^2.2.4", "vue": "^3.4.25", "vue-i18n": "^11.1.5", "vue-router": "^4.3.2", "xe-utils": "^3.7.5"}, "devDependencies": {"@types/bootstrap": "^5.2.6", "@types/js-cookie": "^3.0.6", "@types/node": "^22.7.4", "@vitejs/plugin-vue": "^5.0.3", "@vue/tsconfig": "^0.7.0", "fdir": "^6.4.6", "npm-run-all": "^4.1.5", "sass": "^1.75.0", "typescript": "^5.4.5", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.0.2", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-devtools": "^7.7.6", "vue-tsc": "^2.0.14"}, "type": "module"}