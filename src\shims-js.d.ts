declare module "vite-plugin-cdn-import" {
  import type { Plugin } from "vite";
  interface Options {
    modules: Array<{
      name: string;
      var: string;
      path: string;
    }>;
  }
  export function Plugin(options: Options): Plugin;
}

declare module "*.js";

// 声明 Vue 实例上的全局属性
import api from "@/api";

declare module "@vue/runtime-core" {
  interface ComponentCustomProperties {
    $api: typeof api;
    $ut: any; // XEUtils
    $lo: any; // lodash
    $t: (key: string, values?: Record<string, any>) => string; // i18n
    $i18n: {
      locale: string;
      t: (key: string, values?: Record<string, any>) => string;
    };
  }
}