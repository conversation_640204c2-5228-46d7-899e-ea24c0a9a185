
import { defineStore } from "pinia";
import { ref, watch } from "vue";
import { useDark } from "@vueuse/core";

const useThemeStore = defineStore("theme", () => {
  const isDark = useDark();
  const themeToggler = ref(isDark.value);
  const docElm = document.documentElement;

  const changeTheme = () => {
    isDark.value = !isDark.value;
  };

  watch(isDark, (newValue) => {
    themeToggler.value = newValue;
    docElm.dataset.bsTheme = newValue ? "dark" : "light";
  }, { immediate: true });

  return {
    isDark,
    themeToggler,
    changeTheme,
  };
});

export default useThemeStore;
