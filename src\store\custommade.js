import { defineStore } from "pinia";
import { ref } from "vue";
import { getGroupList, GroupConfig } from "@/mixins/group.js";
const useCustomMadeStore = defineStore(
  "CustomMade",
  () => {
    const activeComponentName = ref("ModelGroupTabs");
    const setactiveComponentName = (activeName) => {
      activeComponentName.value = activeName;
    };

    const activeGroup = ref({
      code: GroupConfig.danxi.code,
      groupID: GroupConfig.danxi.value,
      modelID: "",
      modelCode: "",
      modelCodeName: "",
      sizeID: "",
      clientAddressID: "",
      clientAddress: "",
      itemID: "",
      sizeCode: "",
      clientPersonID: "",
      clientPersonName: "",
    }); // Merged activeTitle into activeGroup
    const setActiveGroup = (code, groupID, modelID, modelCode, modelCodeName) => {
      activeGroup.value.code = code;
      activeGroup.value.groupID = groupID;
      activeGroup.value.modelID = modelID;
      activeGroup.value.modelCode = modelCode;
      activeGroup.value.modelCodeName = modelCodeName;
      // groupData.value.modelCodeName = modelCodeName;
      if (groupData.value) {
        groupData.value.forEach(model => {
          if (model.code === code) {
            model.modelID = modelID
            model.modelCode = modelCode
            model.modelCodeName = modelCodeName
          }
        })
      }
      var group = groupData.value.find(item => item.value === groupID)
      if (group && group.sizeID) {
        activeGroup.value.sizeID = group.sizeID;
        activeGroup.value.sizeCode = group.sizeCode;

      } else {
        activeGroup.value.sizeID = null;
        activeGroup.value.sizeCode = null;
      }
    };
    const setActiveGroupItemID = (itemID, itemText) => {
      activeGroup.value.itemID = itemID;
      activeGroup.value.itemText = itemText;
    }
    const setActiveGroupAddress = (addressID, address) => {
      activeGroup.value.addressID = addressID;
      activeGroup.value.address = address;
    }
    const setActiveGroupClientPerson = (personID, personName) => {
      activeGroup.value.clientPersonID = personID;
      activeGroup.value.clientPersonName = personName;
    }
    const groupData = ref([]);
    const isDataLoaded = ref(false);
    const isLoading = ref(false);
    const modelElemListLoading = ref(false);

    const setModelElemListLoading = (loading) => {
      modelElemListLoading.value = loading;
    };

    const setGroupData = (data) => {

      groupData.value = data;
      if (activeGroup.value.groupID) {
        activeGroup.value.modelID = data.find(item => item.value === activeGroup.value.groupID)?.modelID;
        activeGroup.value.modelCode = data.find(item => item.value === activeGroup.value.groupID)?.modelCode;
        activeGroup.value.modelCodeName = data.find(item => item.value === activeGroup.value.groupID)?.modelCodeName;
      }
      isDataLoaded.value = true;
      isLoading.value = false;
    };
    const setGroupDataByGroupId = (groupID, sizeID, sizeCode) => {
      var group = groupData.value.find(item => item.value === groupID)
      if (group) {
        group.sizeCode = sizeCode
        group.sizeID = sizeID
      }
      if (activeGroup.value.groupID === groupID) {
        activeGroup.value.sizeID = sizeID
        activeGroup.value.sizeCode = sizeCode
      }
    }
    const setLoading = (loading) => {
      isLoading.value = loading;
    };
    const setSizeColumnDataByGroupID = (groupID, sizeColumnData) => {
      var group = groupData.value.find(item => item.value === groupID)
      if (group) {
        group.sizeColumnData = sizeColumnData
      }
    }
    const setActiveGroupTab = (groupSetting) => {
      groupData.value.forEach(group => {
        var item = groupSetting.find(id => group.value === id);
        if (item) {
          group.isShow = true
        } else {
          group.isShow = false
        }
      })
      var _activeGroup = groupSetting.find(id => activeGroup.value.groupID === id);
      if (!_activeGroup) {
        var _findGroup = groupData.value.find(item => item.isShow);
        if (_findGroup) {
          activeGroup.value.groupID = _findGroup.value;
          activeGroup.value.modelID = _findGroup.modelID;
          activeGroup.value.code = _findGroup.code;
          activeGroup.value.modelCode = _findGroup.modelCode;
          activeGroup.value.modelCodeName = _findGroup.modelCodeName;
        }
      }
    }
    const setBodyListDataByGroupID = (groupID, bodyListData) => {
      var group = groupData.value.find(item => item.value === groupID)
      if (group) {
        group.bodyListData = bodyListData
      }
    }
    const resetData = () => {
      groupData.value = [];
      isDataLoaded.value = false;
      isLoading.value = false;
    };

    // API调用方法（您可以替换为实际的API调用）
    const loadGroupData = async () => {
      try {
        setLoading(true);
        const data = await getGroupList();
        data.forEach(item => {
          item.sizeColumnData = []
          item.bodyListData = []
          item.modelElemListGroup = []
          item.modelElemList = []
          item.itemID = ""
        })
        setGroupData(data);
        return data;
      } catch (error) {
        console.error("Failed to load data from API:", error);
        setLoading(false);
        throw error;
      }
    };
    const setModelElemListDataByModelID = (modelID, modelElemListData) => {
      groupData.value.forEach(group => {
        if (group.modelID == modelID) {
          group.modelElemList = modelElemListData
        }
      })
    }
    const setModelElemDefaultByModelID = (modelID, modelElemListData) => {
      groupData.value.forEach(group => {
        if (group.modelID === modelID && group.modelElemListGroup) {
          group.modelElemListGroup.forEach(itemlist => {
            var findList = modelElemListData.find(e => e.modelElemListID === itemlist.modelElemListID);
            if (findList && itemlist.modelElemIdSeleced !== findList.modelElemID) {
              itemlist.modelElemIdSeleced = findList.modelElemID
              if (findList.modelElemID === null) {
                itemlist.modelElemSelected = null
              }
              itemlist.modelElems.forEach(elem => {
                if (elem.modelElemID === findList.modelElemID) {
                  elem.isSelected = true;
                  // console.log("款式明细默认数据发生变化:", elem.modelElemCode)
                  itemlist.modelElemSelected = elem
                  console.log("款式明细默认数据发生变化:", elem.modelElemCode)
                } else {
                  elem.isSelected = false;
                  // itemlist.modelElemSelected = null
                }
              })
            }

          })
        }
      })
    }
    const getModelElemListGroupData = (
      groupName
    ) => {
      return groupData.value.find(
        (item) => item.groupName === groupName
      );
    };
    const getModelElemListGroupDataByCode = (
      code
    ) => {
      return groupData.value.find(
        (item) => item.code === code
      );
    };

    const activeModelListElems = ref([]);
    const setActiveModelElemListData = (elems, modelElem) => {
      activeModelListElems.value = elems;
      activeModelListElems.value.modelElemName = modelElem
    };
    const setActiveModelElemList = (elem) => {
      return new Promise(resolve => {
        activeModelListElems.value.modelElems.forEach(item => {
          if (elem.modelElemID == item.modelElemID) {
            item.isSelected = true
          } else {
            item.isSelected = false
          }
        });
        activeModelListElems.value.modelElemIdSeleced = elem.modelElemID
        activeModelListElems.value.modelElemSelected = elem
        resolve(activeModelListElems.value)
      })

    };
    const getActiveModelElemListData = () => {
      return activeModelListElems.value;
    };

    const collapseActiveState = ref({});
    const setCollapseActive = (tab, active) => {
      collapseActiveState.value[tab] = active;
    };

    return {
      activeComponentName,
      setactiveComponentName,
      activeGroup,
      setActiveGroup,
      setSizeColumnDataByGroupID,
      setModelElemListDataByModelID,
      setModelElemDefaultByModelID,
      setBodyListDataByGroupID,
      setActiveGroupTab,
      groupData,
      isDataLoaded,
      isLoading,
      modelElemListLoading,
      setGroupData,
      setActiveGroupItemID,
      setActiveGroupAddress,
      setActiveGroupClientPerson,
      setGroupDataByGroupId,
      setLoading,
      setModelElemListLoading,
      resetData,
      loadGroupData,
      getModelElemListGroupData,
      getModelElemListGroupDataByCode,
      activeModelListElems,
      setActiveModelElemListData,
      getActiveModelElemListData,
      setActiveModelElemList,
      collapseActiveState,
      setCollapseActive,
    };
  }
);

export default useCustomMadeStore;
