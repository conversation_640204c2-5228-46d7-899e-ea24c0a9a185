# alpine镜像用的是Alpine Linux内核，比ubuntu内核要小很多。
FROM harbor.xmmtm.com/library/nginx:1.19-alpine
#FROM nginx:1.19.2-alpine

#设置工作目录 
WORKDIR /usr/share/nginx/html/
RUN rm -rf /usr/share/nginx/html/*
#复制编译好的项目到工作目录
COPY ./dist /usr/share/nginx/html
#删除配置文件
RUN rm /etc/nginx/nginx.conf
COPY nginx.conf /etc/nginx/nginx.conf
# ADD startup.sh /usr/share/nginx/html
#设置镜像端口为80
EXPOSE 80
EXPOSE 443
# RUN chmod +x startup.sh
# CMD ["/usr/share/nginx/html/startup.sh"]
CMD ["nginx","-g","daemon off;"]