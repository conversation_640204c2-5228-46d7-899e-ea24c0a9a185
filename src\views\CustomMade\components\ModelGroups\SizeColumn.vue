<template>
  <div class="model-wrapper p-2">
    <div style="height: 45px;" class="row align-items-center g-0">
      <div class="col-md-2">
        <el-button type="info" text @click="goBack">
          <el-icon>
            <Back />
          </el-icon>{{ $t('custommade.modelelem.back') }}</el-button>
      </div>
      <div class="col-md-3 ">
        <el-text type="success">
          {{ activeGroup.sizeCode }}
        </el-text>
      </div>
    </div>
    <div>
      <SizeData ref="SizeData" @getSizeData="getSizeData">
      </SizeData>
    </div>
    <div class="scroll-container">
      <el-table :data="sizeColumnData" border style="width: 100%" highlight-current-row>
        <el-table-column type="index" width="42" />
        <el-table-column prop="sizeColumnName" label="名称" width="102" />
        <el-table-column prop="standard1" label="标准规格" width="66" />
        <el-table-column prop="fix1" label="标准修正" width="150">
          <template #default="scope">
            <el-input-number style="width: 125px;" v-model="scope.row.fix1" :precision="2" :step="1" :min="scope.row.fix1Min" :max="scope.row.fix1Max" clearable controls-position="right" size="small">
              <template #prefix>
                <span>{{ scope.row.fix1Min }}</span>
              </template>
              <template #suffix>
                <span>{{scope.row.fix1Max}}</span>
              </template>
              <template #decrease-icon>
                <el-icon>
                  <Minus />
                </el-icon>
              </template>
              <template #increase-icon>
                <el-icon>
                  <Plus />
                </el-icon>
              </template>
            </el-input-number>
          </template>
        </el-table-column>
        <el-table-column prop="finish" label="成衣" width="70">
          <template #default="scope">
            <!-- {{ (scope.row.fix1??0)+scope.row.finish }} -->
            {{ getFinish(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column prop="standard" label="参考" width="70" />
      </el-table>
    </div>
  </div>
</template>

<script lang="ts">
import useCustomMadeStore from "@/store/custommade";
import { Back, CircleCloseFilled, Minus, Plus } from "@element-plus/icons-vue";
import SizeData from "./Size.vue";
import XEUtils from "xe-utils";
export default {
  name: "SizeColumn",
  components: {
    Back,
    CircleCloseFilled,
    Minus,
    Plus,
    SizeData,
  },
  data() {
    return {
      activeComponentsStore: useCustomMadeStore(),
      sizeColumnData: [] as any[],
    };
  },
  computed: {
    activeGroup() {
      return this.activeComponentsStore.activeGroup;
    },
  },
  created() {
    this.loadData();
  },
  methods: {
    goBack() {
      this.activeComponentsStore.setactiveComponentName("ModelGroupTabs");
    },
    getFinish(row: any) {
      return XEUtils.toFixed((row.fix1 ?? 0) + row.standard1, 2);
    },
    loadData() {
      var groupData = this.activeComponentsStore.groupData;
      if (groupData && this.activeGroup) {
        var findData = groupData.find(
          (g) => g.value === this.activeGroup.groupID
        );
        if (findData) {
          this.sizeColumnData = findData.sizeColumnData;
        }
      }
    },
    getSizeData(data: any) {
      this.activeComponentsStore.setGroupDataByGroupId(
        this.activeComponentsStore.activeGroup.groupID,
        data.id,
        data.code
      );
      // console.log(data);
      this.sizeColumnData.forEach((item) => {
        var value = 0;
        if (
          item.sizeColumnCode === "LeftSleeveLength" ||
          item.sizeColumnCode === "RightSleeveLength"
        ) {
          value = XEUtils.get(data, "sleeveLength");
        } else if (
          item.sizeColumnCode === "LeftTrouserLong" ||
          item.sizeColumnCode === "RightTrouserLong"
        ) {
          value = XEUtils.get(data, "netTrouserLong");
        } else {
          value = XEUtils.get(data, XEUtils.camelCase(item.sizeColumnCode));
        }
        item.fix1 = item.fix1 ?? 0;
        item.standard1 = value;
        item.finish = value + item.fix1;
        item.standard = value;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.model-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-container {
  flex: 1;
  overflow-y: auto;
}
</style>