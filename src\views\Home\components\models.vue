<template>
  <div style="position: relative; width: 100%; height: calc(100vh - 250px);">
    <el-space ref="scrollContainer" wrap class="scroll-area" style="width: 100%; height: 100%; overflow-x: hidden; overflow-y: auto;" fill v-infinite-scroll="loadMore" :infinite-scroll-disabled="disabled" :infinite-scroll-distance="500" :infinite-scroll-immediate="false">
      <!-- State 1: Initial Loading -->
      <el-row v-if="isInitialLoading" :gutter="24" style="width: 100%;">
        <el-col v-for="n in 6" :key="n" :xs="24" :sm="12" :md="8" :lg="6" :xl="6" :xxl="4">
          <div class="container-fluid py-3" style="height: 500px">
            <el-skeleton style="width: 100%; height: 100%" animated>
              <template #template>
                <el-skeleton-item variant="image" style="width: 100%; height: 350px;" />
                <div style="padding: 14px;">
                  <el-skeleton-item variant="p" style="width: 50%" />
                  <div style="display: flex; align-items: center; justify-items: space-between; margin-top: 16px; height: 16px;">
                    <el-skeleton-item variant="text" style="margin-right: 16px;" />
                    <el-skeleton-item variant="text" style="width: 30%;" />
                  </div>
                </div>
              </template>
            </el-skeleton>
          </div>
        </el-col>
      </el-row>

      <!-- State 2: No Data -->
      <div v-else-if="!isInitialLoading && ModelData.length === 0 && noMore" class="no-data-fullscreen">
        <p class="status-text">{{ $t("home.models.noData") }}</p>
      </div>

      <!-- State 3: Has Data -->
      <div v-else class="box p-6 mb-4 rounded-3 d-flex flex-column" style="width: 100%; min-height: 100%;">
        <transition-group tag="div" class="card-container flex-grow-1" name="staggered-fade" @before-enter="beforeEnter" @enter="enter" @leave="leave" :css="false">
          <el-col v-for="(item, index) in ModelData" :key="item.modelID" :data-index="index" :xs="24" :sm="12" :md="8" :lg="6" :xl="6" :xxl="4" style="padding-left: 12px; padding-right: 12px;">
            <div class="container-fluid py-3" style="height: 400px">
              <div class="modern-product-card">
                <div @click="goShopEvent(item)" class="product-image-container">
                  <template v-if="item.imageUrl">
                    <img :src="item.imageUrl" class="product-image" :alt="item.modelCode" />
                  </template>
                  <template v-else>
                    <div class="image-placeholder">
                      <i class="bi bi-image" style="font-size: 3rem; color: var(--bs-gray-400);"></i>
                    </div>
                  </template>
                  <div class="product-overlay">
                    <div class="overlay-actions">
                      <el-button type="primary" size="large" round class="action-btn">
                        <i class="bi bi-eye me-2"></i>
                        {{ $t("home.models.viewdetails", "查看详情") }}
                      </el-button>
                    </div>
                  </div>
                </div>
                <div class="product-content">
                  <h5 class="product-title">{{item.modelCode}}</h5>
                  <div class="product-meta">
                    <span class="product-date">
                      <i class="bi bi-calendar3 me-1"></i>
                      {{ formatDate({cellValue:item.createOn },'yyyy-MM-dd')}}
                    </span>
                    <div class="product-actions">
                      <el-button type="primary" size="small" circle @click.stop="addToCart(item)" class="cart-btn">
                        <i class="bi bi-cart-plus"></i>
                      </el-button>
                      <el-button type="default" size="small" circle class="favorite-btn">
                        <i class="bi bi-heart"></i>
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </transition-group>

        <!-- Status indicators for "Has Data" state -->
        <div>
          <p v-if="loading && !isInitialLoading" class="status-text">{{ $t("home.models.loading") }}</p>
        </div>
        <div class="d-flex flex-column">
          <div class="flex-grow-1 nomore">
            <p v-if="noMore && ModelData.length > 0" class="status-text">{{ $t("home.models.noData") }}</p>
          </div>
        </div>
      </div>
    </el-space>

    <el-backtop :right="200" :bottom="100" target=".scroll-area">
      <el-icon>
        <Top />
      </el-icon>
    </el-backtop>

  </div>
</template>

<script>
import { router } from "@/router";
import XEUtils from 'xe-utils';
import gsap from 'gsap';
import { Top } from "@element-plus/icons-vue";
import { useCartStore } from '@/store/cart';

export default {
  name: "HomeModel",
  components: {
    Top,
  },
  props: {
    groupID: {
      type: String,
      default: "",
    },
    inputText: {
      type: String,
      default: ''
    }
  },
  setup() {
    const cartStore = useCartStore();
    return { cartStore };
  },
  data() {
    return {
      loading: false,
      itemsBeforeLoad: 0,
      api: {
        get: "/mtmshop/shopHome/getModelsByModelId",
      },
      maxResultCount: 20,
      skipCount: 0,
      totalCount: 0,
      ModelData: [],
      noMore: false,
    }
  },
  watch: {
    groupID: {
      deep: true,
      async handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          await this.clearStateAndReload()
        }
      }
    }
  },
  computed: {
    disabled() {
      return this.loading || this.noMore
    },
    isInitialLoading() {
      return this.loading && this.ModelData.length === 0;
    }
  },
  async created() {
    await this.clearStateAndReload();
  },
  methods: {
    addToCart(item) {
      const product = {
        id: item.modelID,
        name: item.modelCode,
        price: item.price || 99.99, // Assuming a default price if not available
        image: item.imageUrl,
        description: item.modelCodeName,
      };
      this.cartStore.addToCart(product);
      this.$message.success('商品已添加到购物车！');
    },
    async clearStateAndReload() {
      this.ModelData = []
      this.skipCount = 0;
      this.totalCount = 0;
      this.noMore = false
      this.itemsBeforeLoad = 0;
      // Ensure the scroll container is scrolled to the top
      const scrollContainer = this.$refs.scrollContainer?.$el.querySelector('.el-space__inner');
      if (scrollContainer) {
        scrollContainer.scrollTop = 0;
      }
      await this.loadModels(true);
    },
    goShopEvent(item) {
      router.push({ path: "/custommade", name: "CustomMade", state: { modelID: item.modelID, groupID: item.groupID, modelID: item.modelID, modelCode: item.modelCode, modelCodeName: item.modelCodeName } });
    },
    loadMore() {
      return this.loadModels(false);
    },
    async loadModels(isInitialLoad = false) {
      if (this.loading || (this.noMore && !isInitialLoad)) return;
      this.loading = true
      this.itemsBeforeLoad = this.ModelData.length;

      try {
        const result = await this.$api.ActionRequest(this.api.get, {
          groupID: this.groupID,
          maxResultCount: this.maxResultCount,
          skipCount: this.skipCount,
          text: this.inputText
        });

        if (result.items && result.items.length > 0) {
          this.skipCount += result.items.length;
          this.totalCount = result.totalCount;
          this.ModelData.push(...result.items);

          if (this.skipCount >= this.totalCount) {
            this.noMore = true;
          }
        } else {
          this.noMore = true;
        }
      } catch (error) {
        console.error("Failed to get models:", error);
      } finally {
        this.loading = false;
      }

      if (isInitialLoad) {
        await this.$nextTick();
        const container = this.$refs.scrollContainer?.$el.querySelector('.el-space__inner');
        if (container && container.scrollHeight <= container.clientHeight && !this.noMore) {
          await this.loadModels(true);
        }
      }
    },
    formatDate({ cellValue }, format) {
      if (cellValue === null) {
        return null
      }
      return XEUtils.toDateString(this.formatLongDate(cellValue), format || 'yyyy-MM-dd HH:mm:ss')
    },
    formatLongDate(date) {
      var dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
    },
    beforeEnter(el) {
      el.style.opacity = 0
      el.style.transform = 'translateY(100px)'
    },
    enter(el, done) {
      gsap.to(el, {
        opacity: 1,
        y: 0,
        duration: 0.5,
        delay: (el.dataset.index - this.itemsBeforeLoad) * 0.1,
        onComplete: done
      })
    },
    leave(el, done) {
      gsap.to(el, {
        opacity: 0,
        y: -100,
        duration: 0.5,
        delay: el.dataset.index * 0.1,
        onComplete: done
      })
    }
  }
}
</script>
<style lang="scss">
.scroll-area {
  .el-space__inner {
    height: 100%;
  }
}
.el-col {
  border-radius: 4px;
}
.nomore {
  padding-top: 30px;
}
.grid-content {
  border-radius: 4px;
  min-height: 36px;
}
.status-text {
  text-align: center;
  color: #909399;
  margin: 20px 0;
  font-size: 16px;
}
.no-data-fullscreen {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

/* For transition-group, ensure items that are leaving
   don't disrupt the layout of incoming items */
.staggered-fade-leave-active {
  position: absolute;
}
/* Modern Product Card Styles */
.product-card-wrapper {
  padding: 0.75rem;
  height: 100%;
}

.modern-product-card {
  background: var(--bs-body-bg);
  border-radius: 1.25rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--bs-border-color-translucent);
  position: relative;
}

.dark .modern-product-card {
  background: rgba(33, 37, 41, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modern-product-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.product-image-container {
  position: relative;
  height: 280px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.modern-product-card:hover .product-image {
  transform: scale(1.1);
}

.image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: var(--bs-gray-100);
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.modern-product-card:hover .product-overlay {
  opacity: 1;
}

.overlay-actions {
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.modern-product-card:hover .overlay-actions {
  transform: translateY(0);
}

.action-btn {
  background: linear-gradient(
    135deg,
    var(--bs-primary) 0%,
    var(--bs-info) 100%
  );
  border: none;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  box-shadow: 0 4px 15px rgba(var(--bs-primary-rgb), 0.4);
}

.product-content {
  padding: 1.25rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--bs-body-color);
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  line-height: 1.4;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.product-date {
  font-size: 0.875rem;
  color: var(--bs-text-muted);
  display: flex;
  align-items: center;
}

.product-actions {
  display: flex;
  gap: 0.5rem;
}

.cart-btn {
  background: linear-gradient(
    135deg,
    var(--bs-success) 0%,
    var(--bs-teal) 100%
  );
  border: none;
  transition: all 0.3s ease;
}

.cart-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(var(--bs-success-rgb), 0.4);
}

.favorite-btn {
  background: var(--bs-light);
  border: 1px solid var(--bs-border-color);
  color: var(--bs-text-muted);
  transition: all 0.3s ease;
}

.favorite-btn:hover {
  background: var(--bs-danger);
  color: white;
  border-color: var(--bs-danger);
  transform: scale(1.1);
}

.card-container {
  display: flex !important;
  flex-wrap: wrap !important;
  justify-content: flex-start !important;
  margin-left: -12px;
  margin-right: -12px;
}
.card-container .el-col {
  float: none !important;
}
.el-space__item {
  min-height: 100%;
}
</style>
