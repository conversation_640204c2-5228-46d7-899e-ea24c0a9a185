<template>
  <el-dropdown @command="handleLanguageChange" size="large" style="vertical-align: middle">
    <span class="el-dropdown-link">
      <!-- <i class="bi bi-translate"></i> -->
      <i class="bi bi-globe2"></i>
      <!-- <template v-if="languageShow === 'en'">
        <svg t="1749893142331" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19749" width="25" height="25">
          <path d="M555.205 539.275h157.962V394.662H555.205z m-225.508 0h157.341V394.662H329.697z" p-id="19750" fill="#707070"></path>
          <path d="M921.596 0H102.403C45.85 0 0 45.836 0 102.39v819.208C0 978.151 45.85 1024 102.403 1024h819.193c56.563 0 102.401-45.85 102.401-102.402V102.391C1024 45.836 978.161 0 921.596 0zM779.454 603.7H555.23V814.67h-68.194V603.7H263.434V330.238h223.604V222.2h68.194v107.997h224.223v273.503z" p-id="19751" fill="#707070"></path>
        </svg>
      </template>
      <template v-if="languageShow === 'zh'">
        <svg t="1749892902585" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14109" width="25" height="25">
          <path d="M786.823529 180.705882a79.058824 79.058824 0 0 1 79.058824 79.058824v527.058823a79.058824 79.058824 0 0 1-79.058824 79.058824h-527.058823A79.058824 79.058824 0 0 1 180.705882 786.823529v-527.058823A79.058824 79.058824 0 0 1 259.764706 180.705882h527.058823z m-316.235294 193.264942h-140.559059a26.352941 26.352941 0 0 0-26.352941 26.352941v245.940706c0 14.576941 11.806118 26.352941 26.352941 26.352941h131.764706a26.352941 26.352941 0 1 0 0-52.705883h-105.411764V549.647059h105.411764a26.352941 26.352941 0 0 0 26.172236-23.04l0.180706-3.312941a26.352941 26.352941 0 0 0-26.352942-26.352942h-105.411764v-70.26447h114.206117a26.352941 26.352941 0 0 0 0-52.705882z m87.853177 35.117176a26.352941 26.352941 0 0 0-26.352941 26.352941v210.82353c0 14.576941 11.776 26.352941 26.352941 26.352941l3.312941-0.180706a26.352941 26.352941 0 0 0 23.04-26.172235v-114.176a52.705882 52.705882 0 1 1 105.411765 0v114.176a26.352941 26.352941 0 1 0 52.705882 0v-114.176a105.411765 105.411765 0 0 0-158.147765-91.316706v-5.330824a26.352941 26.352941 0 0 0-26.322823-26.352941z" fill="#707070" p-id="14110"></path>
        </svg>
      </template> -->

    </span>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item :command="'zh'">简体中文 ZH-CN</el-dropdown-item>
        <el-dropdown-item :command="'en'">English EN </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
 
<script setup lang="ts">
import { ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import userLanguageStore from "@/store/language";
// import { ElNotification } from "element-plus";
const { locale } = useI18n();
const { t } = useI18n();
// const currentLocale = ref(locale.value);
let languageShow: string = locale.value;
const commonStore = userLanguageStore();
const handleLanguageChange = (val: string) => {
  // console.log(locale);
  // 更新语言设置
  locale.value = val;
  languageShow = val;
  // console.log(commonStore);
  commonStore.setLan(locale.value);
  // 保存到本地存储
  localStorage.setItem("language", val);
  document.title = t("global.sys.sys_pagetitle");

  // 可选：刷新页面以应用新语言
  window.location.reload();
  // languageMessage();
};
// function languageMessage() {
//   ElNotification({
//     title: "Custom Position",
//     message: "I'm at the bottom right corner",
//     position: "bottom-right",
//   });
// }
// 监听语言变化
watch(locale, (newLocale: string) => {
  document.documentElement.setAttribute("lang", newLocale);
});
</script>
 
<style scoped>
.language-switcher {
  padding: 8px;
}

select {
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
}
</style>