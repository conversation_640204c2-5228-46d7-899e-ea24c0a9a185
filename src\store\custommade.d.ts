import { Ref } from "vue";

export interface GroupData {
  label: string;
  name: string;
  code: string;
  value: string;
  modelElemListGroup: any[];
  modelElemList: any[];
  bodyListData: any[];
  sizeColumnData: any[];
  sizeData: any[];
  modelID: string;
  modelCode: string;
  modelCodeName: string;
  sizeID: string;
  sizeCode: string;
  isActive: boolean;
}

export interface ModelElem {
  modelElemName: string;
  modelElemId: number;
  IsSelected: boolean;
  modelElemDesc: string;
  modelElemImg: string;
}

export interface ModelElemList {
  modelElemListCodeName: string;
  modelElemListCode: string;
  modelElemListID: number;
  ModelElemSeleced: any;
  modelElemIdSeleced: string;
  modelElemListDesc: string;
  modelElemListImg: string;
  modelElems: ModelElem[];
}

export interface ActiveGroup {
  code: string;
  groupID: string;
  modelID: string;
  modelCode: string;
  modelCodeName: string;
  sizeID: string;
  sizeCode: string;
  clientAddressID: string;
  clientAddress: string;
  itemID: string;
  itemText: string;
}

export interface CustomMadeStore {
  activeComponentName: Ref<string>;
  setactiveComponentName: (activeName: string) => void;

  activeGroup: Ref<ActiveGroup>;
  setActiveGroup: (
    code: string,
    groupID: string,
    modelID: string,
    modelCode: string,
    modelCodeName: string,
    sizeID: string,
    sizeCode: string
  ) => void;

  groupData: Ref<GroupData[]>;
  isDataLoaded: Ref<boolean>;
  isLoading: Ref<boolean>;

  setGroupData: (data: GroupData[]) => void;
  setActiveGroupTab: (data: []) => void;
  setGroupDataByGroupId: (
    groupID: string,
    sizeID: string,
    sizeCode: string
  ) => void;
  setLoading: (loading: boolean) => void;
  resetData: () => void;

  loadGroupData: () => Promise<GroupData[]>;
  getModelElemListGroupData: (groupName: string) => GroupData | undefined;
  getModelElemListGroupDataByCode: (code: string) => GroupData | undefined;

  activeModelListElems: Ref<ModelElemList>;
  setActiveModelElemListData: (elems: ModelElemList, modelElem: String) => void;
  setActiveModelElemList: (elem: ModelElem) => void;
  getActiveModelElemListData: () => ModelElemList;
}

declare const useCustomMadeStore: () => CustomMadeStore;
export default useCustomMadeStore;
