    # 代码规约
    - 对于重复代码要进行共通化处理
    - 代码要合理使用代码拆分，采用引用的方式，保证每个代码文件的行数不允许超过600行
    - 删除代码时，要确认代码没有使用后才能删除
    - 添加代码时，要确认你的代码不会对其他代码产生影响
    - 添加新功能时，要确认代码中没有相关功能代码的存在，不允许重复代码
    - 添加画面CSS样式的时候，要确保你添加的CSS不对对全局CSS产生影响
    - 重新代码的时候，要确保新代码和原来的代码的功能，画面样式一致
    - 保证代码的设计和design.md的设计一致
    # 解决问题策略
    - 当出现问题的时候，你要检索整个workspace来解决这个问题，要以整个项目的角度来看
    - 当问题超过两次没有解决时，你要换一个思路来解决问题，启动二重思考
    - 当回复内容过多的时候，你要分段回复
    - 当出现问题，你要通过更多的日志来确定问题发生的原因
    - 每次对话结束，你都要报告新功能或者问题解决当前的进度，如果有推荐做的功能优化，要进行报告确认，不允许自己擅自做出其他优化
