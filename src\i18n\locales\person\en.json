{"person": {"head": "Personal Information", "message": "This is the personal center where you can view and edit your personal information. Please ensure that your information is up-to-date so that we can serve you better.", "tab1": "Change Password", "tab2": "Delivery Address", "tab3": "Customer Management", "searchInputPlaceHolder": "Customer Code or Name", "clientPerson": {"editModalTitle": "Edit Customer Information", "addModalTitle": "Add New Customer Information", "deletePersonMessage": "Operation Successful", "height": "Height", "weight": "Weight", "addClientPersonBtn": "Add New Customer", "table": {"code": "Customer Code", "codeName": "Customer Name", "tel": "Telephone", "gender": "Gender", "men": "Men", "women": "Women", "info": "Detailed Information", "operation": "Operation", "edit": "Edit", "delete": "Delete"}}, "editPassword": {"title": "Change Password", "oldPassword": "Old Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "save": "Save", "error": "New password and confirm password do not match", "Successful": "Password changed successfully"}, "editClientPerson": {"code": "Customer Code", "codeName": "Customer Name", "tel": "Telephone", "gender": "Gender", "email": "Email", "height": "Height", "weight": "Weight", "birthDate": "Birth Date", "company": "Company", "department": "Department", "position": "Position", "address": "Address", "remark": "Remark", "isActive": "Is Active", "save": "Save", "birthDatePlaceholder": "Pick a day", "rules_code_required": "Please input Customer Code", "rules_code_length": "Length should be 2 to 20", "rules_codeName_required": "Please input Customer Name", "rules_codeName_length": "Length should be 2 to 50", "rules_tel_required": "Please input Customer Telephone", "success": "Successful"}}}