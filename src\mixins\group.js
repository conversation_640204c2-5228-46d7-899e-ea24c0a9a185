import lodash from 'lodash'
import { useI18n } from "vue-i18n";

export const GroupConfig = {
    danxi: { // 单西
        label: "单西",
        name: '单西',
        code: 'jacket',
        value: "04c73518-5c7e-4112-bc84-08d832e42ad3",
        modelElemListGroup: [],
        modelID: '',
        isActive: true,
        isShow: true,
    },
    xiku: {// 西裤
        label: "西裤",
        isActive: true,
        name: '西裤',
        code: 'trousers',
        modelElemListGroup: [],
        modelID: '',
        value: '96d8aa8e-26e3-402c-bc89-08d832e42ad3',
        isShow: false,
    },
    majia: {// 马夹
        label: "马夹",
        isActive: true,
        isShow: false,
        name: '马夹',
        code: 'waistcoat',
        modelElemListGroup: [],
        modelID: '',
        value: '1f017d3f-74eb-4f70-bc88-08d832e42ad3'
    },
    dayi: {// 大衣
        label: "大衣",
        isShow: false,
        isActive: true,
        name: '大衣',
        modelElemListGroup: [],
        code: 'overcoat',
        modelID: '',
        value: '5aea051e-ff61-4dd6-bc87-08d832e42ad3'
    },
    taoxi: {// 成品-套西
        label: "套西",
        isActive: false,
        name: '',
        modelElemListGroup: [],
        modelID: '',
        value: '9c9ddcf7-7214-4d1c-bc85-08d832e42ad3'
    },
    sanjiantao: {// 成品-三件套
        label: "三件套",
        isActive: false,
        isShow: false,
        name: '',
        modelID: '',
        modelElemListGroup: [],
        value: '0424b56e-91ab-4831-bc86-08d832e42ad3'
    },

    chenyi: {// 衬衣
        label: "衬衣",
        isActive: false,
        isShow: false,
        name: '衬衣',
        code: 'shirt',
        modelElemListGroup: [],
        modelID: '',
        value: 'eb46aaba-a394-4832-7955-08da055d2427'
    },
    lianyiqun: {// 连衣裙
        label: "连衣裙",
        isActive: false,
        code: '',
        name: '连衣裙',
        modelElemListGroup: [],
        modelID: '',
        value: 'F14BFBAF-FB46-4B32-A446-08DB5E5B8660'.toLocaleLowerCase()
    },
    jiake: {// 夹克
        label: "夹克",
        name: '夹克',
        code: 'coat',
        isShow: false,
        modelElemListGroup: [],
        modelID: '',
        isActive: false,
        value: 'fea30081-f3e4-4cf0-9313-a052024bd7ca'
    },
    duanku: {// 短裤
        label: "短裤",
        name: '短裤',
        code: 'shorts',
        isShow: false,
        modelID: '',
        modelElemListGroup: [],
        isActive: false,
        value: 'a24bfbaf-fb46-4b32-a446-08db5e5b8660'
    },
    xiqun: {// 西裙
        label: "西裙",
        isShow: false,
        isActive: false,
        modelID: '',
        modelElemListGroup: [],
        name: '西裙',
        code: 'skirt',
        value: 'b8430bd2-4ce8-4909-a241-7f368f3677d8'
    }
}

export function getGroupList() {
    var list = []
    lodash.forEach(GroupConfig, (value, key) => {
        if (value.isActive) {
            list.push(value)
        }

    })
    return list;
}
export function getGroupById(groupid) {
    return getGroupList().find((item, idnex) => {
        if (item.value === groupid) {
            return item
        }

    })
}
export function getGroupSetting() {
    const { t } = useI18n();
    var setting = [
        { label: t("global.group.jacket"), value: 1, groupIDs: [GroupConfig.danxi.value], isActive: true },
        { label: t("global.group.trousers"), value: 4, groupIDs: [GroupConfig.xiku.value], isActive: true },
        { label: t("global.group.waistcoat"), value: 5, groupIDs: [GroupConfig.majia.value], isActive: true },
        { label: t("global.group.overcoat"), value: 6, groupIDs: [GroupConfig.dayi.value], isActive: true },
        { label: t("global.group.suits"), value: 2, groupIDs: [GroupConfig.danxi.value, GroupConfig.xiku.value], divided: true, isActive: true },
        { label: t("global.group.threePiece"), value: 3, groupIDs: [GroupConfig.danxi.value, GroupConfig.xiku.value, GroupConfig.majia.value], isActive: true },
        { label: t("global.group.shirt"), value: 7, groupIDs: [GroupConfig.chenyi.value], isActive: false },
        { label: t("global.group.coat"), value: 8, groupIDs: [GroupConfig.jiake.value], isActive: false },
        { label: t("global.group.westSkirt"), value: 9, groupIDs: [GroupConfig.xiqun.value], isActive: false },
    ]
    setting = setting.filter(a => a.isActive)
    return setting
}
export default {
    name: "GroupMixins",
    data() {
        return {
            GroupConfig: GroupConfig,
        }
    },
    methods: {
        getGroupName(item) {
            const { t } = useI18n();
            var name = item.label
            switch (item.value) {
                case this.GroupConfig.danxi.value:
                    name = t("global.group.jacket")
                    break;
                case this.GroupConfig.xiku.value:
                    name = t("global.group.trousers")
                    break;
                case this.GroupConfig.majia.value:
                    name = t("global.group.waistcoat")
                    break;
                case this.GroupConfig.chenyi.value:
                    name = t("global.group.shirt")
                    break;
                case this.GroupConfig.dayi.value:
                    name = t("global.group.overcoat")
                    break;
                case this.GroupConfig.duanku.value:
                    name = t("global.group.shorts")
                    break;
                case this.GroupConfig.jiake.value:
                    name = t("global.group.coat")
                    break;
                case this.GroupConfig.xiqun.value:
                    name = t("global.group.westSkirt")
                    break;
                default:
                    name = item.label
                    break;

            }
            return name
        },

    }
}