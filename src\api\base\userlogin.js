// import { find, assign } from 'lodash'


const userpai = import.meta.env.VITE_BUILD_SSO_API
export default ({ service, request, faker, tools }) => ({
    /**
     * @description 登录
     * @param {Object} data 登录携带的信息
     */
    SYS_USER_LOGIN(data = {}) {
        return request({
            url: 'sso/user/login',
            method: 'post',
            data,
            baseurl: userpai
        })
    },
    SYS_USER_TOKEN(data = {}) {
        return request({
            url: '/connect/token',
            method: 'post',
            data,
            baseurl: userpai.replace('api/', ''),
            ContentType: 'application/x-www-form-urlencoded'
        })
    },
    SYS_USER_GET_BYID(data) {
        return request({
            url: '/sso/user/getuserbyid',
            method: 'get',
            params: data,
            baseurl: userpai
        })
    },
    SYS_USER_UPDATES(data) {
        return request({
            url: '/sso/user/updates',
            method: 'post',
            data,
            baseurl: userpai
        })
    },
})
