#!/bin/bash

project_name=$1
tag=$2
webhoohrul=$3
committext=$4

if [[ "${tag}" =~ ^p.* ]]; then
  port="32004"
  ports="32444"
  project_environment="Production"
  titlename="CM2智尚工厂[商城端][正式]系统"
fi

if [[ "${tag}" =~ ^t.* ]]; then
  port="30004"
  ports="30444"
  project_environment="Development"
  titlename="CM2智尚工厂[商城端][测试]系统"
fi

cp -f deployment.sh deployment_front.sh
cp -f messages.sh messages_front.sh
sed -i "s#project_name1#$project_name#g" deployment_front.sh
sed -i "s#tag2#$tag#g" deployment_front.sh
sed -i "s#prot3#$port#g" deployment_front.sh
sed -i "s#project_environment4#$project_environment#g" deployment_front.sh
sed -i "s#webhoohrul5#$webhoohrul#g" deployment_front.sh
sed -i "s#committext6#${committext}#g" deployment_front.sh
sed -i "s#titlename7#${titlename}#g" deployment_front.sh
sed -i "s#httpspath8#$httpspath#g" deployment_front.sh
sed -i "s#httpspwd9#$httpspwd#g" deployment_front.sh

if [ "${project_environment}" = "Production" ]; then
  scp deployment_front.sh root@47.251.88.223:/root
  scp messages_front.sh root@47.251.88.223:/root
  ssh -t -t root@47.251.88.223 "chmod a+x ./deployment_front.sh ; ./deployment_front.sh"
fi

if [ "${project_environment}" = "Development" ]; then
  scp deployment_front.sh root@47.251.88.223:/root
  scp messages_front.sh root@47.251.88.223:/root
  ssh -t -t root@47.251.88.223 "chmod a+x ./deployment_front.sh ; ./deployment_front.sh"
fi
