worker_processes auto;
events {
    worker_connections  1024;
    accept_mutex on;
}
http {
  include mime.types;
  default_type application/octet-stream;
  sendfile        on;
  client_header_timeout 600s;
  client_body_timeout 600s;
  keepalive_timeout 75s;
  gzip on;
  gzip_min_length 4k;
  gzip_comp_level 4;
  client_max_body_size 1024m;
  client_header_buffer_size 512k;
  client_body_buffer_size 8m;
  server_names_hash_bucket_size 512;
  proxy_headers_hash_max_size 51200;
  proxy_headers_hash_bucket_size 6400;
  gzip_types application/javascript application/x-javascript text/javascript text/css application/json application/xml;
  proxy_connect_timeout 600s;
  proxy_send_timeout 600s;
  proxy_read_timeout 600s;
  proxy_buffer_size 16k;
  proxy_buffers 4 64k;
  proxy_busy_buffers_size 128k;
  proxy_temp_file_write_size 128k;
  large_client_header_buffers 4 512k;
  #include /etc/nginx/conf.d/*.upstream.conf;
  # upstream mtmserver {
  #   #server ************:30002; # 使用 localhost 作为当前服务器地址
  #   server localhost:30002;
  #   server 127.0.0.1:30002;
  # }
  # upstream messerver {
  #   server ************:30003; # 使用 localhost 作为当前服务器地址
  # }
  # upstream ssoserver {
  #   server ************:30001; # 使用 localhost 作为当前服务器地址
  # }
  server {
    listen       80;
    server_name  localhost;
    root /usr/share/nginx/html;
    index index.html;
    #vue2.0前端项目打包后生成的chunk-vendors文件过大加载太慢解决方案
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 9;
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";
    #charset koi8-r;
    #access_log  /var/log/nginx/host.access.log  main;

    #nginx跟后端服务器连接超时时间(代理连接超时)，这个看需求
    proxy_connect_timeout 5m;# 秒 指定nginx与后端server的连接超时时间，这个超时时间不能超过75s
    #连接成功后，后端服务器响应时间(代理接收超时)，这个 同上
    proxy_send_timeout 5m;# 秒
        #服务端向客户端传输数据的超时时间。
    proxy_read_timeout 5m;# 秒
    location / {
      root   /usr/share/nginx/html;
      index  index.html;
      try_files $uri $uri/ /index.html;
      expires -1;
      #proxy_read_timeout 300;  # 秒
      proxy_send_timeout 300; 
      proxy_read_timeout 300; 
      proxy_connect_timeout 300;
            
      #后端的Web服务器可以通过X-Forwarded-For获取用户真实IP
	  proxy_set_header           Host $host;
	  proxy_set_header           Cookie $http_cookie;
	  proxy_set_header           X-Real-IP $remote_addr;
	  proxy_set_header           X-Forwarded-For $proxy_add_x_forwarded_for;
	  proxy_set_header           HTTP_X_FORWARDED_FOR $remote_addr;
	  proxy_set_header           X-Forwarded-Server $host;

      add_header 'Access-Control-Allow-Origin' '*';
      add_header 'Access-Control-Allow-Credentials' 'true';
      add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
      add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
   
    }
      # 避免浏览器缓存过强
      location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
      expires 30d;
      access_log off;
      add_header Cache-Control "public";
    }
    location ~* /api/ {
      expires -1;
    }
    # location /api/jyymtm {
    #   proxy_pass http://mtmserver;
    #   proxy_set_header Host $host;
    #   proxy_set_header X-Real-IP $remote_addr;
    #   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #   proxy_set_header X-Forwarded-Host $http_host;
    #   proxy_set_header X-Forwarded-Port $server_port;
    #   proxy_set_header X-Forwarded-Proto $scheme;
    # }
    # location /fs/images {
    #   proxy_pass http://mtmserver/fs/images;
    #   proxy_set_header Host $host;
    #   proxy_set_header X-Real-IP $remote_addr;
    #   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #   proxy_set_header X-Forwarded-Host $http_host;
    #   proxy_set_header X-Forwarded-Port $server_port;
    #   proxy_set_header X-Forwarded-Proto $scheme;
    # }
    # location /api/jyymes {
    #   proxy_pass http://messerver;
    #   proxy_set_header Host $host;
    #   proxy_set_header X-Real-IP $remote_addr;
    #   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #   proxy_set_header X-Forwarded-Host $http_host;
    #   proxy_set_header X-Forwarded-Port $server_port;
    #   proxy_set_header X-Forwarded-Proto $scheme;
    # }
    # location /api/sso {
    #   proxy_pass http://ssoserver;
    #   proxy_set_header Host $host;
    #   proxy_set_header X-Real-IP $remote_addr;
    #   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #   proxy_set_header X-Forwarded-Host $http_host;
    #   proxy_set_header X-Forwarded-Port $server_port;
    #   proxy_set_header X-Forwarded-Proto $scheme;
    # }
    # location /connect/token {
    #   proxy_pass http://ssoserver/connect/token;
    #   proxy_set_header Host $host;
    #   proxy_set_header X-Real-IP $remote_addr;
    #   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #   proxy_set_header X-Forwarded-Host $http_host;
    #   proxy_set_header X-Forwarded-Port $server_port;
    #   proxy_set_header X-Forwarded-Proto $scheme;
    # }
    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
      root   /usr/share/nginx/html;
    }
  }
}
