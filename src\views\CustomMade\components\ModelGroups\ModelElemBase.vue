<template>
  <div class="test">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <el-skeleton :rows="2" animated />
      <el-skeleton :rows="2" animated />
      <el-skeleton :rows="2" animated />
    </div>

    <!-- 数据内容 -->
    <TransitionGroup v-else name="list" :duration="3000" appear>
      <div v-for="(item,index) in data" :if="item===item" :key="index" class="card mb-3 " @mouseover="addHoverClass" @mouseout="removeHoverClass" style="max-width: 540px;" @click="GoModelElemListEvent">
        <div class="row g-0" style="cursor: pointer;border-radius: 5px;">
          <div class="col-md-4" style="display: flex; align-items: center; justify-content: center;">
            <img :src="item.modelElemBaseImg" class="img-fluid rounded-start" alt="..." style="max-height: 100%; max-width: 100%;">
          </div>
          <div class="col-md-8">
            <div class="card-body">
              <h5 class="card-title">{{groupname}} {{ item.modelElemBaseName }}</h5>
              <p class="card-text">{{item.modelElemBaseDesc}}.</p>
              <p class="card-text"><small class="text-muted">Last updated 3 mins ago</small></p>
            </div>
          </div>
        </div>
      </div>
    </TransitionGroup>
  </div>
</template>


<script>
import useCustomMadeStore from "@/store/custommade";

export default {
  name: "ModelElembase",
  props: {
    groupname: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      data: [],
      activeComponentsStore: null,
      checkDataInterval: null,
      // 定义组件的响应式数据
    };
  },
  created() {
    // 在组件创建时初始化store
    this.activeComponentsStore = useCustomMadeStore();
  },
  mounted() {
    // 在组件挂载后尝试加载数据
    this.loadGroupData();

    // 设置一个定时器，定期检查数据是否已加载
    this.checkDataInterval = setInterval(() => {
      if (this.activeComponentsStore && this.activeComponentsStore.isDataLoaded && this.data.length === 0) {
        this.loadGroupData();
        clearInterval(this.checkDataInterval);
      }
    }, 100);

    // 5秒后清除定时器，避免无限循环
    setTimeout(() => {
      if (this.checkDataInterval) {
        clearInterval(this.checkDataInterval);
      }
    }, 5000);
  },
  beforeUnmount() {
    // 清理定时器
    if (this.checkDataInterval) {
      clearInterval(this.checkDataInterval);
    }
  },
  watch: {
    // 监听 store 中的数据变化
    'activeComponentsStore.groupData': {
      handler(newData, oldData) {
        if (newData && newData.length > 0 && (!oldData || oldData.length === 0)) {
          this.loadGroupData();
        }
      },
      deep: true
    },
    // 监听数据加载状态
    'activeComponentsStore.isDataLoaded': {
      handler(isLoaded, wasLoaded) {
        if (isLoaded && !wasLoaded) {
          this.loadGroupData();
        }
      }
    },
    // 监听 groupname 变化
    groupname: {
      handler(newGroupname, oldGroupname) {
        if (newGroupname !== oldGroupname) {
          this.loadGroupData();
        }
      }
    }
  },
  methods: {
    loadGroupData() {
      if (!this.activeComponentsStore) {
        console.warn('❌ Store not initialized yet');
        return;
      }
      // 检查数据是否已加载
      if (!this.activeComponentsStore.isDataLoaded) {
        console.log('⏳ Data not loaded yet, waiting...');
        this.data = [];
        return;
      }

      // 检查 orderData 是否存在
      if (!this.activeComponentsStore.groupData || this.activeComponentsStore.groupData.length === 0) {
        console.warn('❌ No order data available');
        this.data = [];
        return;
      }

      // 根据groupname获取对应的数据
      const groupData = this.activeComponentsStore.getModelElemBaseDataByCode(this.groupname);

      if (groupData && groupData.modelElemBase && groupData.modelElemBase.length > 0) {
        this.data = groupData.modelElemBase;
      } else {
        // 如果没有找到数据，尝试通过groupName查找
        const groupDataByName = this.activeComponentsStore.getModelElemBaseData(this.groupname);

        if (groupDataByName && groupDataByName.modelElemBase && groupDataByName.modelElemBase.length > 0) {
          this.data = groupDataByName.modelElemBase;
        } else {
          this.data = [];
        }
      }
    },
    getGroupModelElemBaseData(groupname) {
      return this.activeComponentsStore.getModelElemBaseData(groupname);
    },
    GoModelElemListEvent(item) {
      this.activeComponentsStore.setActiveModelElems(item.modelElems);
      this.activeComponentsStore.setactiveComponentName('ModelElem');
    },
    toggleClasses(element, classes, action) {
      classes.forEach(className => {
        element.classList[action](className);
      });
    },
    addHoverClass(event) {
      this.toggleClasses(event.currentTarget, ['border', 'border-2', 'border-secondary'], 'add');
    },
    removeHoverClass(event) {
      this.toggleClasses(event.currentTarget, ['border', 'border-2', 'border-secondary'], 'remove');
    }
  },
  computed: {
    isLoading() {
      return !this.activeComponentsStore || this.activeComponentsStore.isLoading || !this.activeComponentsStore.isDataLoaded;
    }
  },

}
</script>

<style lang="scss">
.loading-container {
  padding: 20px;

  .el-skeleton {
    margin-bottom: 20px;
  }
}

.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>
