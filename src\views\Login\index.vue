<template>
  <main class="bgimage">
    <div class="login-container">
      <div class="login-wrap">
        <h2 style="color: white;">{{ $t("login.login") }}</h2>
        <div class="error-text">{{ loginForm.loginErrorText }}</div>
        <div class="login-form">
          <div class="input-group">
            <!-- <span class="input-icon">

            </span> -->
            <!-- <input type="text" :placeholder="$t('login.username')" required autocomplete="no" v-model="loginForm.username" @input="loginForm.loginErrorText = ''" /> -->
            <el-input v-model="loginForm.username" size="large" @input="loginForm.loginErrorText = ''" :placeholder="$t('login.username')" required autocomplete="no" clearable>
              <template #prefix>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              </template>
            </el-input>
          </div>
          <div class="input-group">
            <!-- <el-icon><Lock /></el-icon> -->
            <el-input :placeholder="$t('login.password')" size="large" required autocomplete="no" v-model="loginForm.password" @input="loginForm.loginErrorText = ''" type="password" show-password clearable>
              <template #prefix>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                </svg>
              </template>
            </el-input>
            <!-- <input type="password" :placeholder="$t('login.password')" required autocomplete="no" v-model="loginForm.password" @input="loginForm.loginErrorText = ''" /> -->
          </div>
          <button type="button" class="btn-login" @click="handleLogin" :disabled="loginForm.isLoading">
            {{ loginForm.isLoading ?$t('login.logining')  : $t('login.loginBtn') }}
          </button>
        </div>
        <div class="tips">
          <!-- {{ loginForm.loginErrorText }} -->
        </div>
      </div>
    </div>
    <footer class="container footer mb-2">
      <Footer />
    </footer>
  </main>
</template>

<script lang="ts" setup>
import Footer from "@/components/Footer.vue";
import { reactive } from "vue";
import { useRouter } from "vue-router";
import { useUserStore } from "@/store/user";
// import { ElMessage } from "element-plus";
const store = useUserStore();
const router = useRouter();

const loginForm = reactive({
  username: "",
  password: "",
  authority: "xiaomamtm",
  loginErrorText: "",
  isLoading: false,
});

const handleLogin = async () => {
  if (!loginForm.username || !loginForm.password) {
    loginForm.loginErrorText = "用户名和密码不能为空";
    return;
  }

  loginForm.isLoading = true;
  loginForm.loginErrorText = "";

  try {
    await store
      .login(loginForm)
      .then((res) => {
        if (res) {
          router.push({ name: "Home" });
        }
      })
      .catch((res) => {
        var str = res;
        if (res === 2 || res === 3) {
          str = "用户名或密码错误";
        }
        // if (res === 3) {
        //   str = "密码错误";
        // }
        if (res === 4) {
          str = "用户未激活";
        }
        loginForm.loginErrorText = str;
        loginForm.isLoading = false;
      });
  } catch (error) {
    if (typeof error === "string") {
      loginForm.loginErrorText = error;
    } else {
      loginForm.loginErrorText = "登录失败，请稍后再试";
    }
  } finally {
    loginForm.isLoading = false;
  }
};
</script>

<style lang="scss" scoped>
:root {
  --login-wrap-bg: rgba(255, 255, 255, 0.1);
  --login-text-color: #f8f8f8;
  --input-bg: rgba(255, 255, 255, 0.2);
  --input-border: rgba(255, 255, 255, 0.3);
  --input-text-color: #f8f8f8;
  --input-placeholder-color: rgba(255, 255, 255, 0.7);
  --input-icon-color: #000;
  --bgimage-bg: #f0f2f5;
  --footer-color: #999;
  --login-box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

html[data-bs-theme="dark"] {
  --login-wrap-bg: rgba(33, 37, 41, 0.75);
  --login-text-color: #f8f8f8;
  --input-bg: rgba(255, 255, 255, 0.1);
  --input-border: rgba(255, 255, 255, 0.2);
  --input-text-color: #f8f8f8;
  --input-placeholder-color: rgba(255, 255, 255, 0.5);
  --input-icon-color: #f8f8f8;
  --bgimage-bg: #212529;
  --footer-color: #6c757d;

  --login-box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.6);
}

$primary-color: #3a7bd5;
$secondary-color: #3a6073;

.bgimage {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: var(
    --bgimage-bg
  ); /* Set a background color for the top/bottom bars */
  display: flex;
  flex-direction: column; /* Layout children vertically */
  // backdrop-filter: blur(50px);
  // -webkit-backdrop-filter: blur(100px);
  // background-color: #4c4033;
  &::before {
    content: "";
    position: absolute;
    top: 10%; /* Center the image vertically */
    left: 0;
    width: 100%;
    height: 80%; /* Image takes 80% of the height */
    background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)),
      url("./images/login2.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
}

.login-container {
  position: relative; // To be on top of the overlay
  display: flex;
  justify-content: flex-end; // Move to the right
  align-items: center;
  width: 100%;
  flex-grow: 1; /* Allow this container to grow and push the footer down */
  padding-right: 10%; // Add some padding from the right edge
}

.login-wrap {
  width: 400px;
  padding: 40px;
  background: var(--login-wrap-bg);
  border-radius: 20px;
  box-shadow: var(--login-box-shadow);
  backdrop-filter: blur(100px);
  -webkit-backdrop-filter: blur(100px);
  border: 1px solid var(--input-border);
  animation: fadeIn 1s ease-in-out;
  color: var(--login-text-color);

  h2 {
    text-align: center;
    margin-bottom: 25px;
    font-weight: 600;
    font-size: 2.2rem;
    text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.5);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.error-text {
  font-size: 14px;
  color: #ff6b6b;
  margin-bottom: 15px;
  text-align: center;
  min-height: 20px;
  font-weight: 500;
}

.login-form {
  .input-group {
    position: relative;
    margin-bottom: 25px;
  }

  .input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--input-icon-color);
    transition: color 0.3s ease;
  }

  input[type="text"],
  input[type="password"] {
    width: 100%;
    padding: 15px 15px 15px 50px;
    background: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: 10px;
    color: var(--input-text-color);
    font-size: 1rem;
    outline: none;
    transition: all 0.3s ease;

    &::placeholder {
      color: var(--input-placeholder-color);
    }

    &:focus {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.7);
      box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
    }
  }

  .input-group:focus-within .input-icon {
    color: var(--input-icon-color);
  }

  .btn-login {
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: 10px;
    background: linear-gradient(to right, $primary-color, $secondary-color);
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);

    &:hover:not(:disabled) {
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    }

    &:disabled {
      background: #555;
      opacity: 0.7;
      cursor: not-allowed;
    }
  }
}

.tips {
  text-align: right;
  margin-top: 15px;
  font-size: 14px;
  a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    &:hover {
      text-decoration: underline;
    }
  }
}

.footer {
  color: var(--footer-color);
  text-align: center;
  position: relative; /* Ensure it's rendered on top of the ::before pseudo-element */
  width: 100%;
  padding-bottom: 10px; /* Add some space at the bottom */
  // text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.theme-switch-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;

  em {
    margin-left: 10px;
    font-size: 1rem;
    color: var(--login-text-color);
  }
}

.theme-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;

  input {
    opacity: 0;
    width: 0;
    height: 0;
  }
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: #2196f3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196f3;
}

input:checked + .slider:before {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

/*just override what you need*/
// @forward "element-plus/theme-chalk/src/dark/var.scss" with (
//   $bg-color: (
//     "page": #0a0a0a,
//     "": #626aef,
//     "overlay": #1d1e1f,
//   )
// );
</style>
