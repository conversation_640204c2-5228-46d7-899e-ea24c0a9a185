<template>
  <div class="container address-list p-2">
    <div class="row">
      <div class="col align-self-start"> <el-button type="primary" @click="openModal()" class="mb-4">{{this.$t('person.clientPerson.addClientPersonBtn','新增顾客')}}</el-button></div>
      <div class="col-md-3 align-self-end">
        <el-input v-model="searchForm.text" :placeholder="$t('person.searchInputPlaceHolder')" clearable @clear="seacrhEvent()" @keyup.enter="seacrhEvent()">
          <template #append>
            <el-button @click="seacrhEvent()">
              <el-icon class="el-icon--right">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                  <path fill="currentColor" d="m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704"></path>
                </svg>
              </el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
    </div>
    <div class="table-container">
      <el-table :data="addresses" style="width: 100%">
        <el-table-column prop="code" :label="$t('person.clientPerson.table.code')" width="120" />
        <el-table-column prop="codeName" :label="$t('person.clientPerson.table.codeName')" width="120" />
        <el-table-column prop="tel" :label="$t('person.clientPerson.table.tel')" width="130" />
        <el-table-column prop="gender" :label="$t('person.clientPerson.table.gender')" width="80">
          <template #default="scope">
            {{ scope.row.gender?$t('person.clientPerson.table.men'):$t('person.clientPerson.table.women') }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('person.clientPerson.table.info')">
          <template #default="scope">
            {{ format(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('person.clientPerson.table.operation')" width="150">
          <template #default="scope">
            <el-button size="small" @click="openModal(scope.row)">{{ $t('person.clientPerson.table.edit') }}</el-button>
            <el-button size="small" type="danger" @click="delete(scope.row)">{{ $t('person.clientPerson.table.delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container d-flex justify-content-end">
      <el-pagination background layout="prev, pager, next, jumper, ->, total" :total="totalCount" :page-size="searchForm.maxResultCount" v-model:current-page.sync="currentPage" @current-change="handlePageChange" />
    </div>
    <!-- Modal -->
    <el-dialog v-model="dialogVisible" :title="modalTitle">
      <EditClientPerson v-if="dialogVisible" :clientPersonForm="selectRow" @success="success" />
    </el-dialog>

  </div>
</template>

<script>
import countries from '@/assets/countries.json';
import EditClientPerson from '@/components/ClientPerson.vue'
export default {
  name: 'ClientPerson',
  components: {
    EditClientPerson,
  },
  data() {
    return {
      addresses: [],
      currentPage: 1,
      pageSize: 20,
      totalCount: 0,
      api: {
        get: "/mtmshop/pserson/getClientPerson",
        delete: "/mtmshop/pserson/deleteClientPerson",
      },

      searchForm: {
        text: "",
        maxResultCount: 20,
        skipCount: 0,
      },
      dialogVisible: false,
      modalTitle: '',
      selectRow: {},
      countries: countries
    };
  },
  created() {
    this.loadData();
  },
  methods: {
    handlePageChange(page) {
      this.searchForm.skipCount = (page - 1) * this.searchForm.maxResultCount
      this.loadData()
    },
    seacrhEvent() {
      this.searchForm.skipCount = 0
      this.loadData()
    },
    async loadData() {
      const res = await this.$api.ActionRequest(this.api.get, this.searchForm);
      if (res) {
        this.totalCount = res.totalCount
        this.addresses = res.items
      }
    },
    openModal(row = null) {
      if (row) {
        this.modalTitle = this.$t("person.clientPerson.editModalTitle");
        this.selectRow = { ...row };
      } else {
        this.modalTitle = this.$t("person.clientPerson.addModalTitle");
        this.selectRow = {}; // Assign a temporary unique ID
      }
      this.dialogVisible = true;
    },
    success() {
      this.dialogVisible = false
      this.loadData()
    },

    async delete(addressToDelete) {
      const res = await this.$api.ActionRequest(this.api.delete, addressToDelete);
      this.loadData()
      this.$message.success(this.$t("person.clientPerson.deletePersonMessage"));
    },
    formatDate({ cellValue }, format) {
      if (cellValue === null) {
        return null
      }
      return this.$ut.toDateString(this.formatLongDate(cellValue), format || 'yyyy-MM-dd HH:mm:ss')
    },
    formatLongDate(date) {
      var dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
    },
    format(row) {
      var bdata = ''
      if (row.birthDate) {
        bdata = this.formatDate({ cellValue: row.birthDate }, 'yyyy-MM-dd')
      }
      let parts = [this.$t("person.clientPerson.height") + ':' + row.height, this.$t("person.clientPerson.weight") + ':' + row.weight, row.email, bdata, row.company, row.department, row.position, row.address, row.remark];
      return parts.filter(p => p).join(', ');
    }
  }
}
</script>

<style scoped>
.address-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}
.table-container {
  flex-grow: 1;
  overflow-y: auto;
}
.pagination-container {
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}
</style>

