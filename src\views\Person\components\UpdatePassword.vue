<template>
  <div class="update-password">
    <h3 class="mb-4">{{$t('person.editPassword.title')}}</h3>
    <form @submit.prevent="onSubmit">
      <div class="mb-3">
        <label for="oldPassword" class="form-label">{{$t('person.editPassword.oldPassword')}}</label>
        <input type="password" class="form-control" id="oldPassword" v-model="form.oldPassword">
      </div>
      <div class="mb-3">
        <label for="newPassword" class="form-label">{{$t('person.editPassword.newPassword')}}</label>
        <input type="password" class="form-control" id="newPassword" v-model="form.newPassword">
      </div>
      <div class="mb-3">
        <label for="confirmPassword" class="form-label">{{$t('person.editPassword.confirmPassword')}}</label>
        <input type="password" class="form-control" id="confirmPassword" v-model="form.confirmPassword">
      </div>
      <button type="submit" class="btn btn-primary">{{$t('person.editPassword.save')}}</button>
    </form>
  </div>
</template>

<script setup>
import { reactive } from 'vue';
import { ElMessage } from 'element-plus';

const form = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
});

const onSubmit = () => {
  if (form.newPassword !== form.confirmPassword) {
    ElMessage.error(this.$t('person.editPassword.error'));
    return;
  }
  console.log('submit!', form);
  // 在这里添加调用API更新密码的逻辑
  ElMessage.success(this.$t('person.editPassword.Successful'));
};
</script>

<style scoped>
.update-password {
  padding: 20px;
}
</style>
