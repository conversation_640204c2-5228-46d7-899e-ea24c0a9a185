<template>
  <div id="app" class="app-container">
    <el-config-provider :locale="localelange">
      <RouterView />
    </el-config-provider>
  </div>
</template>
<script setup lang="ts">
import zhCn from "element-plus/es/locale/lang/zh-cn";
import en from "element-plus/es/locale/lang/en";
import { computed, onMounted, watch } from "vue";
import userCommonStore from "@/store/language";
import useThemeStore from "@/store/theme";
import { useI18n } from "vue-i18n";

const commonStore = userCommonStore();
const themeStore = useThemeStore();

const localelange = computed(() => {
  var l = commonStore.lan === "zh" ? zhCn : en;
  return l;
});

const { t } = useI18n();
onMounted(() => {
  document.title = t("global.sys.sys_pagetitle");
});

watch(
  () => themeStore.isDark,
  (isDark) => {
    const html = document.querySelector("html");
    if (html) {
      if (isDark) {
        html.classList.add("dark");
      } else {
        html.classList.remove("dark");
      }
    }
  },
  { immediate: true }
);
</script>
<style lang="scss">
.app-container {
  height: 100vh;
  overflow-y: hidden;
}
.box {
  background-color: var(--bs-tertiary-bg);
}
</style>
