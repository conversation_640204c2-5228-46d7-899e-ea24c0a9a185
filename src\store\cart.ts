import { defineStore } from "pinia";
import XEUtils from "xe-utils";
export const useCartStore = defineStore("cart", {
  state: () => ({
    items: JSON.parse(localStorage.getItem("cartItems") || "[]"), // 购物车商品列表
  }),
  getters: {
    totalItems: (state) =>
      state.items.reduce((acc: any, item: any) => acc + item.quantity, 0),
    totalPrice: (state) =>
      state.items
        .reduce(
          (acc: any, item: any) => acc + item.product.price * item.quantity,
          0
        )
        .toFixed(2),
  },
  actions: {
    addToCart(product: any, quantity = 1) {
      const existingItem = this.items.find(
        (item: any) => item.product.id === product.id
      );
      if (existingItem) {
        existingItem.quantity += quantity;
      } else {
        this.items.push({
          product,
          quantity,
          addedAt: XEUtils.toDateString(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"),
        });
      }
      localStorage.setItem("cartItems", JSON.stringify(this.items));
    },
    removeFromCart(productId: string) {
      this.items = this.items.filter(
        (item: any) => item.product.id !== productId
      );
      localStorage.setItem("cartItems", JSON.stringify(this.items));
    },
    updateQuantity(productId: string, quantity: string) {
      const item = this.items.find(
        (item: any) => item.product.id === productId
      );
      if (item) {
        item.quantity = quantity;
      }
      localStorage.setItem("cartItems", JSON.stringify(this.items));
    },
    clearCart() {
      this.items = [];
      localStorage.setItem("cartItems", JSON.stringify(this.items));
    },
  },
});
