<template>
  <div class="p-5 mb-4 box rounded-3">
    <div class="container-fluid py-5">
      <h1 class="display-5 fw-bold">{{ $t("contacts.head") }}</h1>
      <!-- <p class="col-md-8 fs-4">Access to contacts has long been a feature available within native applications. The Contacts Picker API brings that functionality to web applications.</p>
      <p class="col-md-8 fs-4">Use cases include selecting contacts to message via an email or chat application, selecting a contacts phone number for use with voice over IP (VOIP), or for discovering contacts who have already joined a social platform. User agents can also offer a consistent experience with other applications on a users device.</p> -->
    </div>
  </div>
</template>