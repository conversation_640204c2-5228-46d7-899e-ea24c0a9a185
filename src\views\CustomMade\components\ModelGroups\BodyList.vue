<template>
  <div class="bodylist-wrapper p-2">
    <!-- <div style="height: 45px;" class="row align-items-center g-0">
      <div class="col-md-4 
      me-auto">
        <el-button type="info" text @click="goBack">
          <el-icon>
            <Back />
          </el-icon>{{ $t('custommade.modelelem.back') }}</el-button>
      </div>
      <div class="col-md-8 ">

      </div>
    </div> -->
    <div class="scroll-container">
      <TransitionGroup name="list" :duration="3000" appear>
        <template v-for="(item) in BodyListData" :key="item.bodyListID">
          <div class="mt-4">
            <el-button style="width: 150px;" disabled> {{item.bodyListName  }}</el-button>
            <el-select v-model="item.bodyID" style="width: 130px" clearable @change="bodylistchange(item.bodyID,item)">
              <el-option v-for="body in item.bodys" :key="body.id" :label="body.codeName" :value="body.id" />
            </el-select>
            <el-input-number v-model="item.value" :precision="2" :step="0.1" :min="item.min??item.allowMin" :max="item.max??item.allowMax" controls-position="right" clearable>
              <template #prefix>
                <span>{{ item.min }}</span>
              </template>
              <template #suffix>
                <span>{{item.max}}</span>
              </template>
              <template #decrease-icon>
                <el-icon>
                  <Minus />
                </el-icon>
              </template>
              <template #increase-icon>
                <el-icon>
                  <Plus />
                </el-icon>
              </template>
            </el-input-number>
          </div>
        </template>
      </TransitionGroup>
    </div>
  </div>
</template>

<script lang="ts">
import useCustomMadeStore from "@/store/custommade";
import { Back, CircleCloseFilled, Minus, Plus } from "@element-plus/icons-vue";
export default {
  name: "BodyList",
  components: {
    Back,
    CircleCloseFilled,
    Minus,
    Plus,
  },
  data() {
    return {
      bodListLoading: false,
      activeComponentsStore: useCustomMadeStore(),
      BodyListData: [] as any[],
    };
  },
  watch: {},
  computed: {
    activeGroup() {
      return this.activeComponentsStore.activeGroup;
    },
  },
  created() {
    this.loadData();
  },
  methods: {
    loadData() {
      var groupData = this.activeComponentsStore.groupData;
      if (groupData && this.activeGroup) {
        var findData = groupData.find(
          (g) => g.value === this.activeGroup.groupID
        );
        if (findData) {
          this.BodyListData = findData.bodyListData;
        }
      }
    },
    goBack() {
      this.activeComponentsStore.setactiveComponentName("ModelGroupTabs");
    },
    bodylistchange(value: string, item: any) {
      var dto = item.bodys.find((item: any) => item.id === value);
      if (dto != null) {
        if (value) {
          item.allowMax = dto.allowMax;
          item.allowMin = dto.allowMin;
          item.max = dto.max;
          item.min = dto.min;
          if (item.allowMax < item.value && item.value) {
            item.value = item.allowMax;
          }
          if (item.allowMin > item.value && item.value) {
            item.value = item.allowMin;
          }
        }
      } else {
        item.value = null;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.bodylist-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-container {
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
}
</style>