<template>
  <div class="container">
    <nav class="navbar navbar-expand-lg mb-4 border-bottom">
      <button class="navbar-toggler border-0 shadow-none" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown" aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>
      <!-- 导航 -->
      <div class="collapse navbar-collapse" id="navbarNavDropdown">
        <ul class="navbar-nav ms-auto">
          <!-- <li class="nav-item text-uppercase" v-for="route in routes" :key="route.path">
            <router-link :to="route.path" class="nav-link" :title="route.children[0].name" :class="{ active: isActive(route.path) }">
              <i class="bi bi-house-fill" v-if="route.path === `${siteUrl}/`"></i>
              {{ route.path !== `${siteUrl}/` ? route.children[0].name : "" }}
            </router-link>
          </li> -->
          <li class="nav-item text-uppercase">
            <router-link to="/" class="nav-link" :title="$t('global.nav.sys_home')">
              <i class="bi bi-house-fill"></i>
            </router-link>
          </li>

          <li class="nav-item text-uppercase">
            <router-link to="/custommade" class="nav-link" :title="$t('global.nav.sys_custommade')">
              <!-- 下单定制页面 -->
              <el-dropdown @command="groupoSettingEevent">
                <i class="bi bi-bag-check-fill"></i>
                <template #dropdown>
                  <el-dropdown-menu style="vertical-align:bottom !important">
                    <el-dropdown-item v-for="item in groupSetting" :command="item.groupIDs" :divided="item.divided" :key="item.value">{{ item.label }}</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </router-link>
          </li>
          <li class="nav-item text-uppercase">
            <router-link to="/cart" class="nav-link" :title="$t('global.nav.sys_cart')">
              <el-badge :value="cartStore.totalItems" :hidden="cartStore.totalItems === 0" :max="99" class="cart-badge">
                <i class="bi bi-cart" ref="cartIcon"></i>
              </el-badge>
            </router-link>
          </li>
          <li class="nav-item text-uppercase">
            <router-link to="/orderlist" class="nav-link" :title="$t('global.nav.sys_orderlist')">
              <!-- 订单列表 -->
              <i class="bi bi-card-list"></i>
              <!-- <el-icon style="vertical-align:bottom !important">
                <List />
              </el-icon> -->
            </router-link>
          </li>
          <li class="nav-item text-uppercase">
            <router-link to="/person" class="nav-link" :title="$t('global.nav.sys_personinfo')">
              <!-- 个人中心 -->
              <i class="bi bi-person"></i>
            </router-link>
          </li>
          <!-- <li class="nav-item text-uppercase">
            <router-link to="/about" class="nav-link" :title="$t('global.nav.sys_about')">
              {{ $t("global.nav.sys_about") }}
            </router-link>
          </li> -->
          <!-- <li class="nav-item text-uppercase">
            <router-link to="/contacts" class="nav-link" :title="$t('global.nav.sys_contacts')">
              {{ $t("global.nav.sys_contacts") }}
            </router-link>
          </li> -->
          <li class="nav-item text-uppercase">
            <router-link to="#" class="nav-link" title="LogOut">
              <i class="bi bi-box-arrow-right" @click="loginOut"></i>
            </router-link>
          </li>
          <li class="nav-item text-uppercase">
            <router-link to="" class="nav-link" title="LogOut">
              <!-- 语言切换 -->
              <languagechange />
            </router-link>
          </li>
        </ul>
        <!-- 主题切换 -->
        <themebtn />
      </div>
    </nav>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { useRouter } from "vue-router";
import languagechange from "@/components/languagechange.vue";
import themebtn from "@/components/themebtn.vue";
import { useUserStore } from "@/store/user";
import { useCartStore } from "@/store/cart";
import { getGroupSetting } from "@/mixins/group.js";
import useCustomMadeStore from "@/store/custommade.js";
// import { List } from "@element-plus/icons-vue";
const userStore = useUserStore();
const router = useRouter();
const cartStore = useCartStore();
const activeComponentsStore = useCustomMadeStore();
const groupSetting = getGroupSetting();
const cartIcon = ref(null);
let initialCartItems = cartStore.totalItems;

// watch(
//   // () => cartStore.totalItems,
//   // (newVal, oldVal) => {
//   //   if (newVal > oldVal && cartIcon.value) {
//   //     cartIcon.value.classList.add("cart-icon-animation");
//   //     setTimeout(() => {
//   //       cartIcon.value.classList.remove("cart-icon-animation");
//   //     }, 500); // 动画持续时间
//   //   }
//   // }
// );

const activeRoute = computed(() => router.currentRoute.value.path);
const groupoSettingEevent = (value: any) => {
  activeComponentsStore.setActiveGroupTab(value);
  router.push({ path: "/custommade", state: { groupSetting: value } });
};
const loginOut = (e: Event) => {
  e.preventDefault();
  userStore.logout();
  router.push({ name: "Login" });
};
</script>

<style lang="scss" scoped>
ul {
  list-style: none;
}

.cart-badge .el-badge__content {
  background-color: #6c757d; /* 不突兀的灰色 */
  border-radius: 10px;
  padding: 0.2em 0.5em;
  font-size: 0.75em;
  line-height: 1;
  white-space: nowrap;
  text-align: center;
}
.navbar {
  .el-dropdown {
    vertical-align: baseline !important;
  }
}
.cart-icon-animation {
  animation: bounce 0.5s;
}

@keyframes bounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
</style>
