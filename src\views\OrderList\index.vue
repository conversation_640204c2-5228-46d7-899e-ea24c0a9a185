<template>
  <div class="order-list-page container">
    <el-card shadow="always" class="order-card">
      <template #header>
        <div class="d-flex justify-content-between align-items-center">
          <h2 class="mb-0">
            <i class="bi bi-receipt me-2"></i>
            我的订单
          </h2>
        </div>
      </template>

      <div class="order-content" v-loading="loading" element-loading-text="加载中..." element-loading-spinner="el-icon-loading" element-loading-background="rgba(255, 255, 255, 0.8)">
        <!-- 搜索区域 -->
        <div class="search-area mb-3">
          <el-input v-model="searchKey" placeholder="请输入订单号或商品名称" class="search-input me-2" clearable>
            <template #append>
              <el-button @click="handleSearch">
                <i class="bi bi-search"></i> 搜索
              </el-button>
            </template>
          </el-input>
        </div>

        <!-- 表格区域 -->
        <div class="table-container">
          <el-table v-if="orderList.length > 0" :data="orderList" style="width: 100%" row-key="orderId" :row-class-name="tableRowClassName">
            <el-table-column prop="orderId" label="订单号" width="180"></el-table-column>
            <el-table-column label="商品信息">
              <template #default="{ row }">
                <div v-for="item in row.items" :key="item.productId" class="d-flex align-items-center mb-2">
                  <img :src="item.productImage || ''" :alt="item.productName" class="img-fluid rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                  <div>
                    <h6 class="mb-0">{{ item.productName }}</h6>
                    <p class="text-muted small mb-0">¥{{ item.price.toFixed(2) }} x {{ item.quantity }}</p>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="totalAmount" label="订单总额" width="150" align="center">
              <template #default="{ row }">
                <span class="fw-bold text-danger">¥{{ row.totalAmount.toFixed(2) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="orderStatus" label="订单状态" width="120" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.orderStatus)">{{ getStatusText(row.orderStatus) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="orderDate" label="下单时间" width="180" align="center">
              <template #default="{ row }">
                {{ formatDateTime(row.orderDate) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template #default="{ row }">
                <el-button type="primary" size="small" link @click="viewOrderDetails(row.orderId)">查看详情</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div v-else class="text-center py-5">
            <el-empty description="您还没有任何订单！">
              <!-- <el-button type="primary" @click="$router.push('/')">去购物</el-button> -->
            </el-empty>
          </div>
        </div>

        <!-- 分页区域 -->
        <div class="pagination-container">
          <el-pagination background layout="prev, pager, next, jumper, ->, total" :total="totalOrders" :page-size="pageSize" :current-page="currentPage" @current-change="handlePageChange" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";

interface OrderItem {
  productId: string;
  productName: string;
  productImage: string;
  price: number;
  quantity: number;
}

interface Order {
  orderId: string;
  items: OrderItem[];
  totalAmount: number;
  orderStatus: "pending" | "processing" | "shipped" | "delivered" | "cancelled";
  orderDate: string;
}

const searchKey = ref("");

const router = useRouter();

const orderList = ref<Order[]>([]);
const loading = ref(true);
const currentPage = ref(1);
const pageSize = ref(10);
const totalOrders = ref(0);

// 模拟的获取订单列表函数
const getOrderList = async (params: {
  page: number;
  pageSize: number;
  searchKey?: string;
}) => {
  return new Promise<{ data: Order[]; total: number }>((resolve) => {
    setTimeout(() => {
      const allOrders: Order[] = [
        {
          orderId: "ORD001",
          items: [
            {
              productId: "P001",
              productName: "商品A",
              productImage: "",
              price: 100.0,
              quantity: 1,
            },
            {
              productId: "P002",
              productName: "商品B",
              productImage: "",
              price: 50.0,
              quantity: 2,
            },
          ],
          totalAmount: 200.0,
          orderStatus: "delivered",
          orderDate: "2023-01-15T10:00:00Z",
        },
        {
          orderId: "ORD002",
          items: [
            {
              productId: "P003",
              productName: "商品C",
              productImage: "",
              price: 20.0,
              quantity: 5,
            },
          ],
          totalAmount: 100.0,
          orderStatus: "pending",
          orderDate: "2023-01-16T11:30:00Z",
        },
        {
          orderId: "ORD003",
          items: [
            {
              productId: "P004",
              productName: "商品D",
              productImage: "",
              price: 150.0,
              quantity: 1,
            },
          ],
          totalAmount: 150.0,
          orderStatus: "shipped",
          orderDate: "2023-01-17T14:00:00Z",
        },
        {
          orderId: "ORD004",
          items: [
            {
              productId: "P005",
              productName: "商品E",
              productImage: "",
              price: 30.0,
              quantity: 3,
            },
          ],
          totalAmount: 90.0,
          orderStatus: "processing",
          orderDate: "2023-01-18T09:00:00Z",
        },
        {
          orderId: "ORD005",
          items: [
            {
              productId: "P006",
              productName: "商品F",
              productImage: "",
              price: 80.0,
              quantity: 1,
            },
          ],
          totalAmount: 80.0,
          orderStatus: "cancelled",
          orderDate: "2023-01-19T16:00:00Z",
        },
        {
          orderId: "ORD006",
          items: [
            {
              productId: "P007",
              productName: "商品G",
              productImage: "",
              price: 120.0,
              quantity: 2,
            },
          ],
          totalAmount: 240.0,
          orderStatus: "delivered",
          orderDate: "2023-01-20T10:00:00Z",
        },
        {
          orderId: "ORD007",
          items: [
            {
              productId: "P008",
              productName: "商品H",
              productImage: "",
              price: 45.0,
              quantity: 1,
            },
          ],
          totalAmount: 45.0,
          orderStatus: "pending",
          orderDate: "2023-01-21T11:30:00Z",
        },
        {
          orderId: "ORD008",
          items: [
            {
              productId: "P009",
              productName: "商品I",
              productImage: "",
              price: 70.0,
              quantity: 3,
            },
          ],
          totalAmount: 210.0,
          orderStatus: "shipped",
          orderDate: "2023-01-22T14:00:00Z",
        },
        {
          orderId: "ORD009",
          items: [
            {
              productId: "P010",
              productName: "商品J",
              productImage: "",
              price: 95.0,
              quantity: 1,
            },
          ],
          totalAmount: 95.0,
          orderStatus: "processing",
          orderDate: "2023-01-23T09:00:00Z",
        },
        {
          orderId: "ORD010",
          items: [
            {
              productId: "P011",
              productName: "商品K",
              productImage: "",
              price: 60.0,
              quantity: 2,
            },
          ],
          totalAmount: 120.0,
          orderStatus: "cancelled",
          orderDate: "2023-01-24T16:00:00Z",
        },
        {
          orderId: "ORD011",
          items: [
            {
              productId: "P012",
              productName: "商品K",
              productImage: "",
              price: 60.0,
              quantity: 2,
            },
          ],
          totalAmount: 120.0,
          orderStatus: "cancelled",
          orderDate: "2023-01-24T16:00:00Z",
        },
      ];

      // 根据搜索关键词筛选
      let filteredOrders = allOrders;
      if (params.searchKey) {
        const keyword = params.searchKey.toLowerCase();
        filteredOrders = allOrders.filter(
          (order) =>
            order.orderId.toLowerCase().includes(keyword) ||
            order.items.some((item) =>
              item.productName.toLowerCase().includes(keyword)
            )
        );
      }

      const start = (params.page - 1) * params.pageSize;
      const end = start + params.pageSize;
      const paginatedData = filteredOrders.slice(start, end);

      resolve({
        data: paginatedData,
        total: filteredOrders.length,
      });
    }, 500); // 模拟网络延迟
  });
};

const fetchOrders = async () => {
  loading.value = true;
  try {
    const response = await getOrderList({
      page: currentPage.value,
      pageSize: pageSize.value,
      searchKey: searchKey.value,
    });
    orderList.value = response.data;
    totalOrders.value = response.total;
  } catch (error) {
    console.error("获取订单列表失败:", error);
    ElMessage.error("获取订单列表失败，请稍后再试！");
  } finally {
    loading.value = false;
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchOrders();
};

const handleSearch = () => {
  currentPage.value = 1; // 重置页码
  fetchOrders();
};

const tableRowClassName = ({
  row,
  rowIndex,
}: {
  row: Order;
  rowIndex: number;
}) => {
  if (row.orderStatus === "cancelled") {
    return "warning-row";
  } else if (row.orderStatus === "delivered") {
    return "success-row";
  }
  return "";
};

const getStatusType = (status: Order["orderStatus"]) => {
  switch (status) {
    case "pending":
      return "info";
    case "processing":
      return "primary"; // 默认使用 primary 类型
    case "shipped":
      return "warning";
    case "delivered":
      return "success";
    case "cancelled":
      return "danger";
    default:
      return "warning";
  }
};

const getStatusText = (status: Order["orderStatus"]) => {
  switch (status) {
    case "pending":
      return "待付款";
    case "processing":
      return "处理中";
    case "shipped":
      return "已发货";
    case "delivered":
      return "已完成";
    case "cancelled":
      return "已取消";
    default:
      return "未知状态";
  }
};

const formatDateTime = (dateTimeString: string) => {
  const date = new Date(dateTimeString);
  return date.toLocaleString(); // 根据本地时间格式化
};

const viewOrderDetails = (orderId: string) => {
  ElMessage.info(`查看订单 ${orderId} 详情`);
  // router.push(`/order/${orderId}`); // 假设有订单详情页
};

onMounted(() => {
  fetchOrders();
});
</script>

<style scoped>
.order-list-page {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "\5FAE\8F6F", Arial, sans-serif;
  height: 100%;
}

.order-card {
  height: 86vh;
  /* height: 100%; */
  border-radius: 12px;
}

.order-card :deep(.el-card__body) {
  height: calc(100% - 60px); /* 减去卡片头部的高度 */
  padding: 0;
  display: flex;
  flex-direction: column;
}

.order-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.search-area {
  flex: none;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 16px;
}

.search-input {
  width: 300px;
}

.table-container {
  flex: 1;
  min-height: 0;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.el-table {
  flex: 1;
  overflow: auto;
}

.pagination-container {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
  padding: 16px 0;
  margin: 0;
  /* background-color: white; */
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: flex-end;
}

.el-table th {
  background-color: #f5f7fa;
}

.fw-bold {
  font-weight: 600 !important;
}

.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}

.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>