<template>
  <div class="d-flex flex-column vh-100">
    <header>
      <Nav />
    </header>
    <main class="flex-grow-1" style="overflow-y: hidden;">
      <router-view v-slot="{ Component }">
        <transition name="slide-fade" mode="out-in">
          <component :is="Component" :key="route.path" />
        </transition>
      </router-view>
    </main>
    <Footer />
  </div>
</template>

<script lang="ts" setup>
import Nav from "@/components/Nav.vue";
import Footer from "@/components/Footer.vue";
import { useRoute } from "vue-router";
const route = useRoute();
</script>
<style lang="scss">
/*
  进入和离开动画可以使用不同
  持续时间和速度曲线。
*/
.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}
</style>
