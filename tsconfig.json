{"extends": "@vue/tsconfig/tsconfig.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts", "vite.config.ts", "dependencies-cdn.ts"], "exclude": ["node_modules", "dist"], "compilerOptions": {"moduleResolution": "node", "target": "esnext", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["element-plus/global", "node", "bootstrap", "vite/client"], "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom", "dom.iterable"], "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true}, "references": []}