<template>
  <div class="address">
    <!-- <el-button @click="goBackEvent">返回</el-button> -->
    <div style="height: 50px;" class="address-wrapper g-0  mt-2">
      <div class="container-fluid row">
        <div class="col-md-2">
          <el-button type="info" text @click="goBack">
            <el-icon>
              <Back />
            </el-icon>{{ $t('custommade.modelelem.back','返回') }}
          </el-button>
        </div>
        <div class="col-md-3">
          <el-button type="success" @click="openModal()">
            新增地址
          </el-button>
        </div>
        <div class="col-md-7">
          <el-input class="mb-2" v-model="searchForm.text" placeholder="输入编码" @keyup.enter="seacrhEvent()" @clear="seacrhEvent()" clearable>
            <template #append>
              <el-button @click="seacrhEvent()">
                <el-icon class="el-icon--right">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                    <path fill="currentColor" d="m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704"></path>
                  </svg>
                </el-icon>
              </el-button>
            </template>
          </el-input>
        </div>
      </div>

    </div>
    <div :infinite-scroll-disabled="disabled" :infinite-scroll-distance="500" :infinite-scroll-immediate="false" v-infinite-scroll="loadMore" class="addresslist">
      <div v-for="(item,index) in itemData" :key="index" class="card mb-2" :class="addressSelected(item)" style="max-width: 540px;height: auto;;cursor: pointer;">
        <div class="row">
          <div class="col" @click="addressSelectChangeEvent(item.id)">
            <div class="card-body">
              <h5 class="card-title">{{ item.contact }}</h5>
              <p class="card-text">{{ formatAddress(item)}}</p>
              <p class="card-text"><small class="text-muted"> {{ formatDate({cellValue:item.createOn },'yyyy-MM-dd')}}</small></p>
              <div class="addressSelect">
                <el-radio v-model="activeGroup.addressID" :value="item.id" @change="addressSelectChangeEvent(item.id)"></el-radio>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="isLoading" class="loading-container text-center">
        <el-skeleton :rows="2" animated />
      </div>
      <div v-if="noMoreData" class="no-more-data-container text-center">
        <span>没有更多了</span>
      </div>
    </div>
    <el-dialog v-model="dialogVisible" :title="'新增收货地址'">
      <ClientAddress @success="success" />
    </el-dialog>
  </div>
</template>


<script>
import useCustomMadeStore from "@/store/custommade";
import XEUtils from 'xe-utils';
import { Back } from "@element-plus/icons-vue";
import ClientAddress from '@/components/ClientAddress.vue'
export default {
  name: "Address",
  components: {
    Back,
    ClientAddress
  },
  props: {
    groupname: {
      type: String,
      // required: false,
    },
  },
  data() {
    return {
      activeComponentsStore: useCustomMadeStore(),
      itemData: [],
      disabled: false,
      isLoading: false,
      noMoreData: false,
      dialogVisible: false,
      searchForm: {
        text: "",
        skipCount: 0,
        maxResultCount: 20,
      },
      api: {
        get: '/mtmshop/pserson/getClientAddress',
      }
    };
  },
  computed: {
    activeGroup() {
      return this.activeComponentsStore.activeGroup;
    },
  },
  created() {
    this.loadData().then(res => {
      this.setDefault()
    })

  },
  methods: {
    addressSelected(item) {
      if (item.id === this.activeGroup.addressID) {
        return 'border border-2 border-secondary'
      }
    },
    success(item) {
      console.log(item)
      this.itemData = []
      this.noMoreData = false
      this.loadData().then(res => {
        this.dialogVisible = false
      })
    },
    openModal() {
      this.dialogVisible = true;
    },
    setDefault() {
      if (this.activeGroup.addressID) {
        return
      }
      var findDefault = this.itemData.find(item => item.isDefault);
      if (findDefault) {
        this.activeComponentsStore.setActiveGroupAddress(findDefault.id, this.formatAddress(findDefault))
      }
    },
    formatAddress(address) {
      let parts = [address.address, address.address1, address.street, address.city, address.province, address.countyCode, address.zip];
      return parts.filter(p => p).join(', ');
    },
    addressSelectChangeEvent(value) {
      var findItem = this.itemData.find(item => item.id === value)
      if (findItem) {
        this.activeComponentsStore.setActiveGroupAddress(value, this.formatAddress(findItem))
      }
    },
    loadMore() {
      this.searchForm.skipCount += this.searchForm.maxResultCount
      this.loadData();
    },
    seacrhEvent() {
      this.searchForm.skipCount = 0
      this.itemData = []
      this.noMoreData = false
      this.disabled = false
      this.loadData();
    },
    goBack() {
      this.activeComponentsStore.setactiveComponentName('ModelGroupTabs');
    },
    formatDate({ cellValue }, format) {
      if (cellValue === null) {
        return null
      }
      return XEUtils.toDateString(this.formatLongDate(cellValue), format || 'yyyy-MM-dd HH:mm:ss')
    },
    formatLongDate(date) {
      var dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
    },
    async loadData() {
      return new Promise(async resolve => {
        if (this.isLoading || this.noMoreData) {
          return;
        }
        this.isLoading = true;
        try {
          const res = await this.$api.ActionRequest(this.api.get, this.searchForm);
          if (res.items.length < this.searchForm.maxResultCount) {
            this.noMoreData = true;
            this.disabled = true;
          }
          this.itemData.push(...res.items);
          resolve(true)
        } catch (error) {
          resolve(true)
          console.error("Failed to load data:", error);
        } finally {
          this.isLoading = false;
          resolve(true)
        }
      })

    }
  },
  mounted() {
    // 在组件挂载后执行的代码
  },
}
</script>

<style lang="scss" scoped>
.address-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}
.address {
  height: 100%;
  overflow: hidden;
  .addressSelect {
    position: absolute;
    right: 20px;
    bottom: 20px;
  }
  .addresslist {
    height: 100%;
    overflow-y: auto;
  }
}
.scroll-container {
  flex: 1;
  overflow-y: auto;
}
</style>
