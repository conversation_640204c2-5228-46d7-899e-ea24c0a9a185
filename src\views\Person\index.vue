<template>
  <div class="person-center container">
    <el-tabs v-model="activeName" class="demo-tabs">
      <el-tab-pane :label="$t('person.tab1')" name="updatePassword">
        <UpdatePassword />
      </el-tab-pane>
      <el-tab-pane :label="$t('person.tab2')" name="addressList" style="height: 100%;">
        <AddressList />
      </el-tab-pane>
      <el-tab-pane :label="$t('person.tab3')" name="clientPerson" style="height: 100%;">
        <ClientPersonVue />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import UpdatePassword from './components/UpdatePassword.vue';
import AddressList from './components/AddressList.vue';
import ClientPersonVue from './components/ClientPerson.vue';
const activeName = ref('updatePassword');
</script>

<style scoped>
.person-center {
  padding: 20px;
  /* height: calc(100vh-180px); */
  /* height: 100%; */
  height: calc(86vh);
}
.demo-tabs {
  height: 100%;
}
.el-tabs__content {
  height: 100% !important;
}
</style>
