<template>
  <el-tour v-model="open">
    <el-tour-step :target="ref1?.$el" title="Upload File">
      <img style="width: 240px" src="https://element-plus.org/images/element-plus-logo.svg" alt="tour.png" />
      <div>Put you files here.</div>
    </el-tour-step>
    <el-tour-step :target="ref2?.$el" title="Save" description="Save your changes" />
    <el-tour-step :target="ref3?.$el" title="Other Actions" description="Click to see other" />
  </el-tour>
</template>

<script>
export default {
  name: "Tour",
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    ref1: {
      type: Object,
      default: null,
    },
    ref2: {
      type: Object,
      default: null,
    },
    ref2: {
      type: Object,
      default: null,
    }
  },
  data() {
    return {
      open: false,
      ref1: null,
      ref2: null,
      ref3: null,
    };
  },
  mounted() {
    this.ref1 = this.ref1;
    this.ref2 = this.ref2;
    this.ref3 = this.ref3;
  },
}
</script>

<style>
</style>