<template>
  <el-tour v-model="tourOpen">
    <el-tour-step :target="groupSettingTarget" title="组设置">
      <img style="width: 240px" src="https://element-plus.org/images/element-plus-logo.svg" alt="tour.png" />
      <div>点击这里可以切换不同的服装组合设置。</div>
    </el-tour-step>
    <el-tour-step :target="ref2" title="保存" description="保存您的更改" />
    <el-tour-step :target="ref3" title="其他操作" description="点击查看其他操作" />
  </el-tour>
</template>

<script>
export default {
  name: "Tour",
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    groupSetting: {
      type: [String, Object],
      default: null,
    },
    ref2: {
      type: [String, Object],
      default: null,
    },
    ref3: {
      type: [String, Object],
      default: null,
    }
  },
  data() {
    return {
      tourOpen: false,
      groupSettingTarget: null,
    };
  },
  watch: {
    open: {
      handler(newVal) {
        this.tourOpen = newVal;
        if (newVal) {
          // 当 tour 打开时，重新初始化目标元素
          this.$nextTick(() => {
            this.initTargets();
          });
        }
      },
      immediate: true
    },
    groupSetting: {
      handler() {
        this.initTargets();
      }
    }
  },
  mounted() {
    this.initTargets();
  },
  methods: {
    initTargets() {
      // 获取 groupSetting 元素
      if (this.groupSetting) {
        if (typeof this.groupSetting === 'string') {
          // 如果是字符串，通过 ID 查找元素
          this.groupSettingTarget = document.getElementById(this.groupSetting);
        } else {
          // 如果是对象，直接使用
          this.groupSettingTarget = this.groupSetting;
        }
      }
    }
  },
}
</script>

<style>
</style>