<template>
  <div>
    <el-form ref="ruleFormRef" :model="currentPerson" :rules="rules" label-width="auto">
      <el-form-item :label="$t('person.editClientPerson.code')" prop="code">
        <el-input v-model="currentPerson.code" />
      </el-form-item>
      <el-form-item :label="$t('person.editClientPerson.codeName')" prop="codeName">
        <el-input v-model="currentPerson.codeName" />
      </el-form-item>
      <el-form-item :label="$t('person.editClientPerson.gender')" prop="gender">
        <el-select v-model="currentPerson.gender" placeholder="Select">
          <el-option v-for="num in [{label: $t('person.clientPerson.table.men'), value: true},{label: $t('person.clientPerson.table.women'), value: false}]" :key="num.label" :value="num.value" :label="num.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('person.editClientPerson.tel')">
        <el-input v-model="currentPerson.tel" />
      </el-form-item>
      <el-form-item :label="$t('person.editClientPerson.email')">
        <el-input v-model="currentPerson.email" />
      </el-form-item>
      <el-form-item :label="$t('person.editClientPerson.height')">
        <!-- <el-input v-model="currentPerson.height" /> -->
        <el-input-number v-model="currentPerson.height" :precision="2" :step="1" :min="10" :max="300" />
      </el-form-item>
      <el-form-item :label="$t('person.editClientPerson.weight')">
        <!-- <el-input v-model="currentPerson.weight" /> -->
        <el-input-number v-model="currentPerson.weight" :precision="2" :step="1" :min="10" :max="1000" />
      </el-form-item>
      <el-form-item :label="$t('person.editClientPerson.birthDate')">
        <!-- <el-input v-model="currentPerson.birthDate" /> -->
        <el-date-picker v-model="currentPerson.birthDate" type="date" :placeholder="$t('person.editClientPerson.birthDatePlaceholder')" />
      </el-form-item>
      <el-form-item :label="$t('person.editClientPerson.company')">
        <el-input v-model="currentPerson.company" />
      </el-form-item>
      <el-form-item :label="$t('person.editClientPerson.department')">
        <el-input v-model="currentPerson.department" />
      </el-form-item>
      <el-form-item :label="$t('person.editClientPerson.position')">
        <el-input v-model="currentPerson.position" />
      </el-form-item>
      <el-form-item :label="$t('person.editClientPerson.address')" prop="address">
        <el-input v-model="currentPerson.address" maxlength="35" />
      </el-form-item>
      <el-form-item :label="$t('person.editClientPerson.remark')">
        <el-input v-model="currentPerson.remark" maxlength="500" type="textarea" />
      </el-form-item>
      <el-form-item :label="$t('person.editClientPerson.isActive')">
        <el-switch v-model="currentPerson.isActive" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="saveAddress('ruleFormRef')">{{ $t('person.editClientPerson.save') }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>

export default {
  name: "EditClientPerson",
  props: {
    clientPersonForm: {
      type: Object,
    },
  },
  data() {
    return {
      dialogShow: false,
      api: {
        GlobalCountryComboStore: '/mtm/combo/GlobalCountryComboStore',
        update: '/mtmshop/pserson/updateClientPerson'
      },
      currentPerson: {
        id: null,
        code: "",
        codeName: "",
        tel: '',
        gender: true,
        isActive: true,
        address: '',
        remark: '',
        clientID: null,
        height: 180,
        weight: null,
        birthDate: null,
        company: null,
        department: null,
        position: null,
      },
      rules: {
        code: [
          { required: true, message: this.$t('person.editClientPerson.rules_code_required'), trigger: 'blur' },
          { min: 2, max: 20, message: this.$t('person.editClientPerson.rules_code_length'), trigger: 'blur' },],
        codeName: [
          { required: true, message: this.$t('person.editClientPerson.rules_codeName_required'), trigger: 'blur' },
          { min: 2, max: 50, message: this.$t('person.editClientPerson.rules_codeName_length'), trigger: 'blur' },],
        tel: [
          { required: true, message: this.$t('person.editClientPerson.rules_tel_required') }
        ],
        // address: [
        //   { required: true, message: '地址必须填写' },
        //   { min: 3, max: 35, message: 'Length should be 3 to 35', trigger: 'blur' }
        // ],
        // globalCountryID: [
        //   { required: true, message: '请选择国家' }
        // ],
      },
      // GlobalCountryComboStore: []
    }
  },
  created() {
    console.log(this.clientPersonForm)
    if (this.clientPersonForm) {
      this.currentPerson = Object.assign(this.currentPerson, this.clientPersonForm)
    }
    this.getCombStore()
  },
  methods: {
    async getCombStore() {
      // await this.$api.ActionRequest(this.api.GlobalCountryComboStore).then(result => {
      //   this.GlobalCountryComboStore = result
      // })
    },
    async saveAddress(ruleFormRef) {
      if (!ruleFormRef) return;
      this.$refs[ruleFormRef].validate(async (valid) => {
        if (valid) {
          await this.$api.ActionRequest(this.api.update, this.currentPerson).then(result => {
            this.$emit("success", true);
          })
        } else {
          console.log('error submit!')
        }
      })
      //   const index = this.addresses.findIndex(a => a.id === this.currentPerson.id);
      //   if (index > -1) {
      //     this.$set(this.addresses, index, this.currentPerson);
      //     this.$message.success('地址更新成功');
      //   } else {
      //     this.addresses.unshift(this.currentPerson); // Add to the beginning
      //     this.$message.success('地址新增成功');
      //   }
    },
  }
}
</script>

<style>
</style>