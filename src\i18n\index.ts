import { createI18n } from "vue-i18n";
// import zh from "@/i18n/locales/zh/index";
// import en from "@/i18n/locales/en/index";
import zh from "@/i18n/locales/zh_CN";
import en from "@/i18n/locales/en";
import ezhCn from "element-plus/es/locale/lang/zh-cn";
import een from "element-plus/es/locale/lang/en";
// 语言包的类型合并
const messages = {
  zh: { ...zh, ...ezhCn },
  en: { ...en, ...een },
};
// localStorage获取当前语言类型(初次本地不存在'language'字段存储，默认设置为'zh_CN')
const lang = localStorage.getItem("language") || "en";
// console.log("初始语言类型", lang);
// console.log(localStorage.getItem("language"));
const i18n = createI18n({
  legacy: false,
  globalInjection: true,
  locale: lang,
  fallbackLocale: "en",
  messages: messages,
});
export default i18n;

// export default new createI18n({
//     legacy: false,
//     locale: lang, // set locale
//     messages: messages, // set locale messages
//     globalInjection: true // 在全局注册`$t`和`$te`
// });
