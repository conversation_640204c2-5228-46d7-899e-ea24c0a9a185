import { createApp } from "vue";

import App from "@/App.vue";
import { router } from "@/router";
import i18n from "@/i18n/index.ts";
import "bootstrap/scss/bootstrap.scss";
import "bootstrap/dist/js/bootstrap.bundle.min.js";
import "bootstrap-icons/font/bootstrap-icons.css";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import "element-plus/theme-chalk/dark/css-vars.css";
import { createPinia } from "pinia";
import plugin from "@/plugin/index.js";
const pinia = createPinia();
const app = createApp(App);
// console.log("当前语言类型", i18n.global.locale);
app
  .use(router)
  .use(ElementPlus, { size: "default", zIndex: 3000 })
  .use(pinia)
  .use(i18n)
  .use(plugin);
router.isReady().then(() => app.mount("#app"));
