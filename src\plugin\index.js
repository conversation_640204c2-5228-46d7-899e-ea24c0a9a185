

// import VCharts from 'v-charts'
// 功能插件
import pluginApi from '@/plugin/api'
import XEUtils from 'xe-utils'
import lodash from 'lodash'
// import XEUtils from 'xe-utils'
export default {
    async install(app, options) {
        // app.config.globalProperties.$utils = XEUtils
        // // Element
        // app.use(ElementUI, {
        //     i18n: (key, value) => { i18n.t(key, value) },
        //     size: 'mini'
        // })
        // 插件
        app.use(pluginApi)
        app.config.globalProperties.$ut = XEUtils
        app.config.globalProperties.$lo = lodash
    }
}
