<template>
  <div class="cart-page container">
    <el-card shadow="always" class="cartCard">
      <template #header>
        <div class="d-flex justify-content-between align-items-center">
          <h2 class="mb-0">
            <i class="bi bi-cart-check-fill me-2"></i>
            我的购物车
          </h2>
          <div>
            <el-button type="danger" plain @click="handleClearCart" :disabled="!cartStore.items.length">
              <i class="bi bi-trash me-2"></i>
              清空购物车
            </el-button>
            <el-button type="success" @click="handleCheckout" :disabled="!cartStore.items.length" class="ms-2">
              <i class="bi bi-bag-check-fill me-2"></i>
              去结算
            </el-button>
          </div>
        </div>
      </template>

      <div v-if="paginatedItems.length > 0" style="height: 100%;" class="cart-content">
        <div class="table-container">
          <el-table :data="paginatedItems" style="width: 100%" row-key="product.id">
            <el-table-column label="商品图片" width="150">
              <template #default="{ row }">
                <img :src="row.product.image " :alt="row.product.name" class="img-fluid rounded" style="width: 100px; height: 100px; object-fit: cover;">
              </template>
            </el-table-column>

            <el-table-column label="商品名称">
              <template #default="{ row }">
                <h5 class="mb-0">{{ row.product.name }}</h5>
                <p class="text-muted small">{{ row.product.description }}</p>
              </template>
            </el-table-column>

            <el-table-column label="单价" width="150" align="center">
              <template #default="{ row }">
                <span class="fw-bold">¥{{ row.product.price.toFixed(2) }}</span>
              </template>
            </el-table-column>

            <el-table-column label="数量" width="200" align="center">
              <template #default="{ row }">
                <el-input-number :model-value="row.quantity" @change="(quantity) => handleQuantityChange(row.product.id, quantity)" :min="1" :max="99" size="small" controls-position="right" style="width: 120px;" />
              </template>
            </el-table-column>

            <el-table-column label="小计" width="150" align="center">
              <template #default="{ row }">
                <span class="fw-bold text-danger">¥{{ (row.product.price * row.quantity).toFixed(2) }}</span>
              </template>
            </el-table-column>

            <el-table-column label="添加时间" width="180" align="center">
              <template #default="{ row }">
                {{row.addedAt}}
              </template>
            </el-table-column>

            <el-table-column label="操作" width="120" align="center">
              <template #default="{ row }">
                <el-button type="danger" size="small" circle @click="handleRemoveFromCart(row.product.id)">
                  <i class="bi bi-trash"></i>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div v-if="paginatedItems.length > 0" class="bottom-container">
        <div class="summary-container">
          <span>总计:</span>
          <span class="summary-items">数量 <span class="summary-count">{{ cartStore.totalItems }}</span></span>
          <span class="summary-price">价格 <span class="summary-total-price">¥{{ cartStore.totalPrice }}</span></span>
        </div>
        <div class="pagination-container">
          <el-pagination background layout="prev, pager, next, jumper, ->, total" :total="cartStore.items.length" :page-size="pageSize" :current-page="currentPage" @current-change="handlePageChange" />
        </div>
      </div>
      <div v-else class="text-center py-5">
        <el-empty description="购物车是空的，快去逛逛吧！">
          <el-button type="primary" @click="$router.push('/')">去首页</el-button>
        </el-empty>
      </div>
    </el-card>

  </div>
</template>

<script setup>
import XEUtils from 'xe-utils'
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { useCartStore } from '@/store/cart';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';

const cartStore = useCartStore();
const router = useRouter();

const cartCard = ref(null);

// --- 分页逻辑 ---
const currentPage = ref(1);
const pageSize = ref(10); // 每页显示5条

const paginatedItems = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return cartStore.items.slice(start, end);
});

const handlePageChange = (page) => {
  currentPage.value = page;
};
// --- 购物车操作 ---
const handleQuantityChange = (productId, quantity) => {
  cartStore.updateQuantity(productId, quantity);
};

const handleRemoveFromCart = (productId) => {
  cartStore.removeFromCart(productId);
};

const handleClearCart = () => {
  ElMessageBox.confirm('确定要清空购物车吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    cartStore.clearCart();
    ElMessage.success('购物车已清空');
  }).catch(() => {
    // 用户取消操作
  });
};

const handleCheckout = () => {
  // 在这里可以添加跳转到结算页面的逻辑
  ElMessage.success('正在跳转到结算页面...');
  // router.push('/checkout');
};



</script>

<style scoped>
.cartCard {
  height: 86vh;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
}

:deep(.el-card__body) {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 0;
}

.cart-page {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "\5FAE\8F6F", Arial, sans-serif;
  display: flex;
  flex-direction: column;
  height: 100%;
  /* 页面高度填充 */
}

.cart-content {
  flex-grow: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.table-container {
  flex-grow: 1;
  overflow-y: auto;
}

.bottom-container {
  border-top: 1px solid #ebeef5;
}

.summary-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 20px;
  font-weight: bold;
}

.summary-container span {
  margin-left: 20px;
}

.summary-count,
.summary-total-price {
  font-size: 1.1em;
  color: #f56c6c;
  margin-left: 8px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
}

.el-card {
  border-radius: 12px;
  flex-grow: 1;
  /* 让 el-card 填充 cart-page 的可用空间 */
}

.el-table th {
  background-color: #f5f7fa;
}

.fw-bold {
  font-weight: 600 !important;
}
</style>
