import { defineStore } from "pinia";
import {
  setCookei<PERSON>,
  removeCookei<PERSON>,
  qsStringify,
  getCookei<PERSON>,
} from "@/utils/auth";
import api from "@/api";
import i18n from "@/i18n";
import useCustomMadeStore from "./custommade";

interface UserState {
  role: string[];
  userid: string;
  username: string;
  clientID: string;
}

export const useUserStore = defineStore("user", {
  state: (): UserState => {
    const userInfo = getCookeies("userinfo");
    return {
      role: userInfo?.userRoles || [],
      userid: userInfo?.userid || "",
      username: userInfo?.name || "",
      clientID: userInfo?.clientID || "",
    };
  },
  getters: {
    roleGetter: (state): string[] => state.role,
    usernameGetter: (state): string => state.username,
  },
  actions: {
    login(data: any) {
      return new Promise(async (resolve, reject) => {
        try {
          const res = await api.SYS_USER_LOGIN({
            username: data.username,
            password: data.password,
            authority: data.authority,
          });
          if (res.result !== 1) {
            reject(res.result);
          } else {
            var conf = {
              grant_type: "password",
              client_id: res.token.client_id,
              clientKey: res.token.clientKey,
              client_secret: res.token.client_secret,
              username: data.username,
              password: data.password,
              // scope: "shop",
            };
            if (!res.userInfo.clientID) {
              reject(i18n.global.t("login.clientIsNull"));
            } else {
              var token = await api.SYS_USER_TOKEN(qsStringify(conf));
              setCookeies("token", token.access_token);
              setCookeies("tokeninfo", token);
              setCookeies("userinfo", res.userInfo);
              this.role = res.userInfo.userRoles;
              this.userid = res.userInfo.userid;
              this.username = res.userInfo.name;
              this.clientID = res.userInfo.clientID;
              resolve(true);
            }
          }
        } catch (err) {
          reject(err);
        }
      });
    },
    logout() {
      return new Promise((resolve, reject) => {
        try {
          const customMadeStore = useCustomMadeStore();
          customMadeStore.resetData();
          removeCookeies("token");
          removeCookeies("tokeninfo");
          removeCookeies("userinfo");
          this.$reset();
          resolve(true);
        } catch (err) {
          reject(err);
        }
      });
    },
    getToken() {
      return getCookeies("token");
    },
    getUserInfo() {
      return new Promise((resolve) => {
        const info = getCookeies("userinfo");
        resolve(info);
      });
    },
  },
});
