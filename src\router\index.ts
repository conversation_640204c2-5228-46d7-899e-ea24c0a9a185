import { createRouter, createWebHistory } from "vue-router";
import { getCookei<PERSON> } from "@/utils/auth";

// const baseUrl = import.meta.env.VITE_BUILD_ADDRESS;
const baseUrl = "";
const Default = () => import("@/layouts/Default.vue");
const Page = () => import("@/layouts/Page.vue");
const CustomMadeModel = () => import("@/layouts/CustomMadeModel.vue");
export const routes = [
  //首页
  {
    path: `${baseUrl}/`,
    component: () => Default(),
    children: [
      {
        path: "",
        name: "Home",
        component: () => import("@/views/Home/index.vue"),
      },
    ],
  },
  //关于
  {
    path: `${baseUrl}/about`,
    component: () => Page(),
    children: [
      { path: "", name: "About", component: () => import("@/views/About.vue") },
    ],
  },
  //联系我们
  {
    path: `${baseUrl}/contacts`,
    component: () => Page(),
    children: [
      {
        path: "",
        name: "Contacts",
        component: () => import("@/views/Contacts.vue"),
      },
    ],
  },
  //购物车
  {
    path: `${baseUrl}/cart`,
    component: () => Default(),
    children: [
      {
        path: "",
        name: "Cart",
        component: () => import("@/views/Cart/index.vue"),
      },
    ],
  },
  //订单
  {
    path: `${baseUrl}/orderlist`,
    component: () => Default(),
    children: [
      {
        path: "",
        name: "OrderList",
        component: () => import("@/views/OrderList/index.vue"),
      },
    ],
  },
  //服装定制
  {
    path: `${baseUrl}/custommade`,
    component: () => CustomMadeModel(),
    children: [
      {
        path: "",
        name: "CustomMade",
        component: () => import("@/views/CustomMade/index.vue"),
      },
    ],
  },
  //个人中心
  {
    path: `${baseUrl}/person`,
    component: () => Default(),
    children: [
      {
        path: "",
        name: "Person",
        component: () => import("@/views/Person/index.vue"),
      },
    ],
  },
  //登录
  {
    path: `${baseUrl}/login`,
    component: () => import("@/views/Login/index.vue"),
    name: "Login",
  },
];

export const router = createRouter({
  history: createWebHistory(),
  routes: routes,
});

const whiteList = ["/login"];

router.beforeEach((to, from, next) => {
  const hasToken = getCookeies("token");
  if (hasToken) {
    if (to.path === "/login") {
      next({ path: "/" });
    } else {
      next();
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next();
    } else {
      next(`/login?redirect=${to.path}`);
    }
  }
});
