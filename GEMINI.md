始终用中文回答所有

# CM2 前端项目介绍

## 项目简介

CM2 是一个基于现代前端技术栈开发的电商平台前端项目，旨在为用户提供流畅、高效的购物体验。项目采用模块化开发，易于维护和扩展。

## 技术栈

- React / Vue（根据实际情况填写）
- TypeScript / JavaScript
- Redux / Vuex 状态管理
- Axios 网络请求
- Sass / Less 样式预处理
- Webpack / Vite 构建工具
- element-plus： 2.10.1
- bootstrap: 5.3.1
- vue-i18n: 11.1.5

## 主要功能

- 商品浏览与搜索
- 购物车管理
- 订单提交与支付
- 用户注册与登录
- 个人中心与订单管理

## 项目结构

```
/src
    /components   # 组件
    /pages        # 页面
    /services     # API 接口
    /store        # 状态管理
    /assets       # 静态资源
```

## 启动方式

1. 安装依赖
   ```bash
   npm install
   ```
2. 启动开发服务器
   ```bash
   npm run dev
   ```
