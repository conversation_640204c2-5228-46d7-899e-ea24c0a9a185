<template>
  <div>
    <el-form ref="ruleFormRef" :model="currentAddress" :rules="rules" label-width="auto">
      <el-form-item label="收货人" prop="contact">
        <el-input v-model="currentAddress.contact" />
      </el-form-item>
      <el-form-item label="联系电话" prop="tel">
        <el-input v-model="currentAddress.tel" />
      </el-form-item>
      <el-form-item label="国家" prop="tel">
        <el-select v-model="currentAddress.globalCountryID" placeholder="Select">
          <el-option v-for="num in GlobalCountryComboStore" :key="num.label" :value="num.value" :label="num.code"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="邮箱">
        <el-input v-model="currentAddress.email" />
      </el-form-item>
      <el-form-item label="州/省" prop="province">
        <el-input v-model="currentAddress.province" />
      </el-form-item>
      <el-form-item label="城市" prop="city">
        <el-input v-model="currentAddress.city" />
      </el-form-item>
      <el-form-item label="街道">
        <el-input v-model="currentAddress.street" />
      </el-form-item>
      <el-form-item label="地址 1" prop="address">
        <el-input v-model="currentAddress.address" maxlength="35" />
      </el-form-item>
      <el-form-item label="地址 2">
        <el-input v-model="currentAddress.address1" maxlength="35" />
      </el-form-item>
      <el-form-item label="邮政编码">
        <el-input v-model="currentAddress.zip" />
      </el-form-item>
      <el-form-item label="默认收货地址">
        <el-switch v-model="currentAddress.isDefault" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="saveAddress('ruleFormRef')">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>

export default {
  name: "UpdateClientAddress",
  props: {
    addressForm: {
      type: Object,
    },
  },
  data() {
    return {
      dialogShow: false,
      api: {
        GlobalCountryComboStore: '/mtm/combo/GlobalCountryComboStore',
        update: '/mtmshop/pserson/updateClientAddress'
      },
      currentAddress: {
        id: null,
        contact: "",
        tel: '',
        email: '',
        globalCountryID: '',
        province: '',
        city: '',
        county: '',
        street: '',
        address: '',
        address1: '',
        remark: '',
        isDefault: false,
        clientID: ''
      },
      rules: {
        contact: [
          { required: true, message: 'Please input contact', trigger: 'blur' },
          { min: 2, max: 20, message: 'Length should be 2 to 20', trigger: 'blur' },],
        tel: [
          { required: true, message: '联系电话必须填写' }
        ],
        address: [
          { required: true, message: '地址必须填写' },
          { min: 3, max: 35, message: 'Length should be 3 to 35', trigger: 'blur' }
        ],
        globalCountryID: [
          { required: true, message: '请选择国家' }
        ],
        province: [{ required: true, message: '请输入州/省' }],
        city: [{ required: true, message: '请输入城市' }],
      },
      GlobalCountryComboStore: []
    }
  },
  created() {
    if (this.addressForm) {
      this.currentAddress = Object.assign(this.currentAddress, this.addressForm)
    }
    this.getCombStore()
  },
  methods: {
    async getCombStore() {
      await this.$api.ActionRequest(this.api.GlobalCountryComboStore).then(result => {
        this.GlobalCountryComboStore = result
      })
    },
    async saveAddress(ruleFormRef) {
      if (!ruleFormRef) return;
      this.$refs[ruleFormRef].validate(async (valid) => {
        if (valid) {
          await this.$api.ActionRequest(this.api.update, this.currentAddress).then(result => {
            this.$emit("success", true);
          })
        } else {
          console.log('error submit!')
        }
      })
      //   const index = this.addresses.findIndex(a => a.id === this.currentAddress.id);
      //   if (index > -1) {
      //     this.$set(this.addresses, index, this.currentAddress);
      //     this.$message.success('地址更新成功');
      //   } else {
      //     this.addresses.unshift(this.currentAddress); // Add to the beginning
      //     this.$message.success('地址新增成功');
      //   }
    },
  }
}
</script>

<style>
</style>