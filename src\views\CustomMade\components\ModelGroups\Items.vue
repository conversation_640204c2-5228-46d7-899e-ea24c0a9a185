<template>
  <!-- <el-button @click="goBackEvent">返回</el-button> -->
  <div style="height: 45px;" class="align-items-center g-0">
    <el-input class="mb-2" v-model="searchForm.text" placeholder="输入物料编码名称" @keyup.enter="seacrhEvent()" @clear="seacrhEvent()" clearable>
      <template #append>
        <el-button @click="seacrhEvent()">
          <el-icon class="el-icon--right">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
              <path fill="currentColor" d="m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704"></path>
            </svg>
          </el-icon>
        </el-button>
      </template>
    </el-input>
  </div>
  <div class="items row justify-content-start" style="margin-left: 3px;" :infinite-scroll-disabled="disabled" :infinite-scroll-distance="500" :infinite-scroll-immediate="false" v-infinite-scroll="loadMore">
    <div v-for="(item,index) in itemData" :key="index" class="card col-sm-24 col-md-12 col-lg-8 mb-2  " :class="itemSelected(item)" style="max-width: 400px;height: auto;cursor: pointer;">
      <div class="row g-0" style="width: 100%;">
        <div class="col-md-3" style="display: flex; align-items: center; justify-content: center;">
          <img style="max-height: 100%; max-width: 100%;" :src="item.image" class="img-fluid rounded-start" alt="">
        </div>
        <div class="col-md-9" @click="itemselectChangeEvent(item.itemID)">
          <div class="card-body">
            <h5 class="card-title">{{ item.itemCode }}</h5>
            <p class="card-text">{{ item.originalItemNo}}</p>
            <p class="card-text"><small class="text-muted"> {{ formatDate({cellValue:item.createOn },'yyyy-MM-dd')}}</small></p>
            <div class="itemselect">
              <el-radio v-model="activeGroup.itemID" :value="item.itemID" @change="itemselectChangeEvent(item.itemID)"></el-radio>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="isLoading" class="loading-container text-center">
      <el-skeleton :rows="2" animated />
    </div>
    <div v-if="noMoreData" class="no-more-data-container text-center">
      <span>没有更多了</span>
    </div>
  </div>

</template>


<script>
import useCustomMadeStore from "@/store/custommade";
import XEUtils from 'xe-utils';
export default {
  name: "Items",
  props: {
    groupname: {
      type: String,
      // required: false,
    },
  },
  data() {
    return {
      activeComponentsStore: useCustomMadeStore(),
      itemData: [],
      disabled: false,
      isLoading: false,
      noMoreData: false,
      searchForm: {
        text: "",
        skipCount: 0,
        maxResultCount: 20,
      },
      api: {
        getitem: '/mtmshop/customMade/GetItem',
      }
    };
  },
  computed: {
    activeGroup() {
      return this.activeComponentsStore.activeGroup;
    },
  },
  created() {
    this.loadData()
  },
  methods: {
    itemSelected(item) {
      if (item.itemID === this.activeGroup.itemID) {
        return 'border border-2 border-secondary'
      }
    },
    itemselectChangeEvent(value) {
      var findItem = this.itemData.find(item => item.itemID === value)
      if (findItem) {
        this.activeComponentsStore.setActiveGroupItemID(value, findItem.itemCode + ":" + findItem.itemCodeName)
      }
    },
    loadMore() {
      this.searchForm.skipCount += this.searchForm.maxResultCount
      this.loadData();
    },
    seacrhEvent() {
      this.searchForm.skipCount = 0
      this.itemData = []
      this.noMoreData = false
      this.disabled = false
      this.loadData();
    },
    goBackEvent() {
      this.activeComponentsStore.setactiveComponentName('ModelGroupTabs');
    },
    formatDate({ cellValue }, format) {
      if (cellValue === null) {
        return null
      }
      return XEUtils.toDateString(this.formatLongDate(cellValue), format || 'yyyy-MM-dd HH:mm:ss')
    },
    formatLongDate(date) {
      var dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
    },
    async loadData() {
      if (this.isLoading || this.noMoreData) {
        return;
      }
      this.isLoading = true;
      try {
        const res = await this.$api.ActionRequest(this.api.getitem, this.searchForm);
        if (res.items.length < this.searchForm.maxResultCount) {
          this.noMoreData = true;
          this.disabled = true;
        }
        this.itemData.push(...res.items);
      } catch (error) {
        console.error("Failed to load data:", error);
      } finally {
        this.isLoading = false;
      }
    }
  },
  mounted() {
    // 在组件挂载后执行的代码
  },
}
</script>

<style lang="scss" scoped>
.items {
  height: 100%;
  overflow-y: auto;
  .itemselect {
    position: absolute;
    right: 20px;
    bottom: 20px;
  }
}
</style>
