<style scoped>
.dark-input .el-input__inner {
  background-color: #1d1e1f;
  color: #fff;
}
</style>
<template>
  <div>
    <!-- {{ $t("test.title") }} -->
    <!-- <el-button type="success" @click="getTest">Success</el-button> -->
    <div class="container">
      <div class="row">
        <div class="col-md-9  align-self-start">
          <div class="mb-4">
            <el-button :color="btnActiveValue===''?'#626aef':''" round @click="btnClickEvent('')"> {{$t("home.group.all")  }}</el-button>
            <template v-for="(item,index) in groupData" :key="index">
              <el-button round :color="item.value===btnActiveValue?'#626aef':''" @click="btnClickEvent(item.value)">{{getGroupName(item)}}</el-button>
            </template>
          </div>
        </div>
        <div class="col-md-3 align-self-end">
          <div class="mb-4">
            <el-input v-model="inputText" :placeholder="$t('home.group.searchinput')" clearable @clear="seacrhEvent()" @keyup.enter="seacrhEvent()" @focus="focusEvent">
              <template #append>
                <el-button @click="seacrhEvent()">
                  <el-icon class="el-icon--right">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                      <path fill="currentColor" d="m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704"></path>
                    </svg>
                  </el-icon>
                </el-button>
              </template>
            </el-input>
            <!-- <el-input v-model="Text" size="large" style="width: 240px" placeholder="Search" :prefix-icon="Search" /> -->
          </div>
        </div>
      </div>
    </div>

    <models :groupID="btnActiveValue" :inputText="inputText" ref="homeModels" />
  </div>
</template>

<script>
import models from "./components/models.vue";
import { useUserStore } from "@/store/user";
import { Search } from "@element-plus/icons-vue";
//import { useI18n } from "vue-i18n";
// import useThemeStore from "@/store/theme";
// import { storeToRefs } from "pinia";
import GroupMixins, { getGroupList } from '@/mixins/group'
const userstore = useUserStore();
const token = userstore.getToken();
export default {
  name: "Home",
  mixins: [GroupMixins],
  components: {
    models
  },
  // setup() {
  //   const themeStore = useThemeStore();
  //   const { isDark } = storeToRefs(themeStore);
  //   return { isDark };
  // },
  data() {
    return {
      Search,
      btnActiveValue: "",
      api: {
        get: "/mtm/test/test1",
        GroupComboStore: "/mtm/combo/GroupClientShowComboStore",
      },
      inputText: "",
      GroupComboStore: [],
    }
  },
  computed: {
    groupData() {
      return getGroupList()
    }
  },
  async created() {
    await this.getCombStore()
  },
  methods: {
    async getCombStore() {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })

    },

    async seacrhEvent() {
      // this.$refs.homeModels.ModelData = []
      await this.$nextTick()
      this.$refs.homeModels.clearStateAndReload()
    },
    async getTest() {
      await this.$api.ActionRequest(this.api.get, { text: "123123" }).then(result => {
        console.log(result)
      })
    },
    focusEvent(event) {
      const inputElement = event.target;
      // 确保 Input 组件允许选择文本
      if (inputElement && inputElement.setSelectionRange) {
        inputElement.setSelectionRange(0, inputElement.value.length); // 选择所有文本
      }
    },
    btnClickEvent(val) {
      this.btnActiveValue = val
    },
    // getGroupName(item) {
    //   return getGroupName(item);
    // }
  }

}
</script>
