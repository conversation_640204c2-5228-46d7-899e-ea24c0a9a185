<template>
  <div class="model-elem-wrapper p-2">
    <div style="height: 45px;" class="row align-items-center g-0 backmodelelemlist">
      <div class="col-md-4 
      me-auto">
        <el-button type="info" text @click="goBackModelElemListEvent">
          <el-icon>
            <Back />
          </el-icon>{{ $t('custommade.modelelem.back','返回') }}</el-button>
      </div>
      <div class="col-md-8 ">
        <el-text type="success">
          {{ modelElemListGroup.modelElemListCode }} {{ modelElemListGroup.modelElemListCodeName }}</el-text>
      </div>
    </div>
    <div class="scroll-container">
      <TransitionGroup name="list" :duration="3000" appear>
        <div v-for="(elem, index) in modelElemListGroup.modelElems" :key="index" class="card mb-3 ;" style="max-width: 540px;" :class="modelelemdisabledEvent(elem)">
          <div class="row g-0" @click="modelElemClickEvent(elem)">
            <div class="col-md-4">
              <template v-if="elem.modelElemImg">
                <img :src="elem.modelElemImg" class="image multi-content" style="max-height: 100%; max-width: 100%; object-fit: contain;" />
              </template>
              <template v-else>
                <el-skeleton-item variant="image" style="width: 100%; height:100%" />
              </template>
            </div>
            <div class="col-md-8">
              <div class="card-body">
                <h6 class="card-title">{{elem.modelElemCode}} {{ elem.modelElemCodeName }}</h6>
                <!-- <p class="card-text">{{ elem.modelElemDesc }}</p> -->
                <p class="card-text"><small class="text-muted"> {{ elem.modelElemDesc }}</small></p>
              </div>
            </div>
          </div>
          <template v-if="!elem.isDisabled">
            <el-checkbox class="position-absolute bottom-0 end-0 p-3" v-model="elem.isSelected" @change="modelElemClickEvent(elem)" :disabled="elem.isDisabled"></el-checkbox>

          </template>
          <!-- <template v-else>
            <el-icon style="width: 13px;" class="position-absolute bottom-0 end-0 m-3">
              <CircleCloseFilled />
            </el-icon>
          </template> -->

        </div>
      </TransitionGroup>
    </div>
  </div>
</template>

<script>
import useCustomMadeStore from "@/store/custommade";
import { Back, CircleCloseFilled } from '@element-plus/icons-vue'
export default {
  name: "ModelElem",
  props: {
    groupname: {
      type: String,
      // required: false,
    },
  },
  components: {
    Back, CircleCloseFilled
  },
  data() {
    return {
      modelElemListGroup: { modelElems: [] },
      activeComponentsStore: useCustomMadeStore(),
      api: {
        getModelElemListByModel:
          "/mtmshop/customMade/GetModelElemListByModel",
      },
    };
  },
  watch: {
    // 监听 store 中的数据变化
    // 'activeComponentsStore.activeModelElems': {
    //   handler(newData, oldData) {
    //     if (newData && newData.length > 0 && (!oldData || oldData.length === 0)) {
    //       this.loadElemData()
    //     }
    //   },
    //   deep: true
    // },
  },
  computed: {
    activeModelElems() {
      return this.activeComponentsStore.activeModelElems;
    }
  },
  mounted() {
    this.loadElemData()
  },
  created() {
    this.loadElemData()
  },
  methods: {
    loadElemData() {
      var elemData = this.activeComponentsStore.getActiveModelElemListData()
      this.modelElemListGroup = elemData
    },
    goBackModelElemListEvent() {
      this.activeComponentsStore.setactiveComponentName('ModelGroupTabs');
    },
    modelElemClickEvent(item) {
      if (item.isDisabled) {
        return
      }
      this.activeComponentsStore.setActiveModelElemList(item);
      this.changeModelElemList(item)
    },
    //获取推算新的版型
    changeModelElemList(elem) {
      //版型默认的款式明细就不计算
      if (this.activeComponentsStore && this.activeComponentsStore.activeModelListElems && this.activeComponentsStore.activeModelListElems.modelElemName === 'modelElemList') {
        this.modelElemListFun(elem)
      }
      if (this.activeComponentsStore && this.activeComponentsStore.activeModelListElems && this.activeComponentsStore.activeModelListElems.modelElemName === 'modelElemListGroup') {
        this.modelElemListGroupFun(elem)
      }
    },
    modelElemListFun(elem) {
      return
    },
    modelElemListGroupFun(elem) {
      const groupData = this.activeComponentsStore.getModelElemListGroupDataByCode(this.activeComponentsStore.activeGroup.code);
      var _modelelemlistGroup = groupData?.modelElemListGroup ?? []
      if (_modelelemlistGroup.length <= 0) {
        return
      }
      var modelElemList = []
      var _modelElemList = groupData?.modelElemList ?? []
      if (_modelElemList.length > 0) {
        modelElemList = _modelElemList.map(item => { if (item.modelElemSelected && item.modelElemSelected !== null) { return { modelElemListID: item.modelElemListID, modelElemID: item.modelElemIdSeleced, isSelected: elem.modelElemListID === item.modelElemListID } } }).filter(item => item != null);
      }
      var modelElemListGroup = _modelelemlistGroup.map(item => { return { modelElemListID: item.modelElemListID, modelElemID: item.modelElemIdSeleced, isSelected: elem.modelElemListID === item.modelElemListID } }
      )

      if (modelElemListGroup.length <= 0) {
        console.log("款式明细选择时:not has data")
      }
      this.getModelElemList(this.activeComponentsStore.activeGroup.modelID, elem.modelIDs, modelElemListGroup, modelElemList)
    },
    async getModelElemList(modelID, modelIDs, modelElemListGroup, modelElemList) {
      this.activeComponentsStore.setModelElemListLoading(true);
      await this.$api.ActionRequest(
        this.api.getModelElemListByModel,
        {
          modelID: modelID,
          modelIDs: modelIDs,
          modelElemListGroup: modelElemListGroup,
          modelElemList: modelElemList,
        }
      ).then(res => {
        if (res.modelID === modelID) {
          console.log(`版型未发生变化:${this.activeComponentsStore.activeGroup.modelCode}`)
          this.activeComponentsStore.setModelElemListLoading(false)
          return
        }
        console.log(`版型发生变化:旧${this.activeComponentsStore.activeGroup.modelCode}:${this.activeComponentsStore.activeGroup.modelCodeName} 新：${res.modelCode}:${res.modelCodeName}`)
        // console.log(res.ModelElemListGroup)
        this.activeComponentsStore.setActiveGroup(this.activeComponentsStore.activeGroup.code, res.groupID, res.modelID, res.modelCode, res.modelCodeName);
        this.activeComponentsStore.setModelElemListDataByModelID(res.modelID, res.modelElemList)
        this.activeComponentsStore.setModelElemDefaultByModelID(res.modelID, res.modelElemListGroup)
        this.activeComponentsStore.setModelElemListLoading(false)
      }).catch(() => {
        this.activeComponentsStore.setModelElemListLoading(false)
      })
    },
    modelelemdisabledEvent(item) {
      var css = "";
      css = item.isSelected ? 'border border-2 border-secondary' : ''
      css += item.isDisabled ? ' modelelemdisabled' : ' modelelemenabled'
      return css
    }
  },
  mounted() {
    // 在组件挂载后执行的代码
  },
}
</script>

<style lang="scss" scoped>
.model-elem-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-container {
  flex: 1;
  overflow-y: auto;
}

.modelelemdisabled {
  cursor: not-allowed;
}

.modelelemenabled {
  cursor: pointer;
}
</style>
