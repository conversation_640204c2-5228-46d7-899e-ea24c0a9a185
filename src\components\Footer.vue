<template>
  <footer class="footer border-top">
    <div class="container-fluid py-4 px-4 d-flex justify-content-between align-items-center">
      <section>
        <Refs /> <a href="#" class="text-decoration-none text-body-secondary" title="Starter by How Bizarre">{{$t('global.sys.sys_company_name')}}</a>
      </section>

      <section class="d-flex align-items-center">
        <div class="me-3">
          <languagechange />
        </div>

        <themebtn />
      </section>
    </div>
  </footer>
</template>
  
<script lang="ts" setup>
import Refs from "@/components/Refs.vue";
import languagechange from "@/components/languagechange.vue";
import themebtn from "@/components/themebtn.vue";
</script>

<style lang="scss" scoped>
.footer {
  background-color: var(--bs-tertiary-bg);
  transition: background-color 0.3s ease;
}

.footer a:hover {
  color: var(--bs-primary) !important;
}
</style>