<template>
  <div class="container address-list p-2">
    <div class="row">
      <div class="col align-self-start"> <el-button type="primary" @click="openModal()" class="mb-4">新增收货地址</el-button></div>
      <div class="col-md-3 align-self-end">
        <el-input v-model="searchForm.text" :placeholder="$t('home.group.searchinput')" clearable @clear="seacrhEvent()" @keyup.enter="seacrhEvent()">
          <template #append>
            <el-button @click="seacrhEvent()">
              <el-icon class="el-icon--right">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                  <path fill="currentColor" d="m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704"></path>
                </svg>
              </el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
    </div>
    <div class="table-container">
      <el-table :data="addresses" style="width: 100%">
        <el-table-column prop="contact" label="收货人" width="120" />
        <el-table-column prop="tel" label="联系电话" width="150" />
        <el-table-column prop="countyCode" label="国家" width="200" />
        <el-table-column prop="isDefault" label="默认地址" width="150">
          <template #default="scope">
            {{ scope.row.isDefault?"是":"" }}
          </template>
        </el-table-column>
        <el-table-column label="收货地址">
          <template #default="scope">
            {{ formatAddress(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" @click="openModal(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteAddress(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container d-flex justify-content-end">
      <el-pagination background layout="prev, pager, next, jumper, ->, total" :total="totalCount" :page-size="searchForm.maxResultCount" v-model:current-page.sync="currentPage" @current-change="handlePageChange" />
    </div>
    <!-- Modal -->
    <el-dialog v-model="dialogVisible" :title="modalTitle">
      <ClientAddress :addressForm="currentAddress" @success="success" />
    </el-dialog>

  </div>
</template>

<script>
import countries from '@/assets/countries.json';
import ClientAddress from '@/components/ClientAddress.vue'
export default {
  name: 'AddressList',
  components: {
    ClientAddress,
  },
  data() {
    return {
      addresses: [],
      currentPage: 1,
      pageSize: 20,
      totalCount: 0,
      api: {
        get: "/mtmshop/pserson/getClientAddress",
        delete: "/mtmshop/pserson/deleteClientAddress",
      },

      searchForm: {
        text: "",
        maxResultCount: 20,
        skipCount: 0,
      },
      dialogVisible: false,
      modalTitle: '',
      currentAddress: {},
      countries: countries
    };
  },
  created() {
    this.loadData();
  },
  methods: {
    handlePageChange(page) {
      this.searchForm.skipCount = (page - 1) * this.searchForm.maxResultCount
      this.loadData()
    },
    seacrhEvent() {
      this.searchForm.skipCount = 0
      this.loadData()
    },
    async loadData() {
      const res = await this.$api.ActionRequest(this.api.get, this.searchForm);
      if (res) {
        this.totalCount = res.totalCount
        this.addresses = res.items
      }
    },
    openModal(address = null) {
      if (address) {
        this.modalTitle = '编辑收货地址';
        this.currentAddress = { ...address };
      } else {
        this.modalTitle = '新增收货地址';
        this.currentAddress = {}; // Assign a temporary unique ID
      }
      this.dialogVisible = true;
    },
    success() {
      this.dialogVisible = false
      this.loadData()
    },

    async deleteAddress(addressToDelete) {
      const res = await this.$api.ActionRequest(this.api.delete, addressToDelete);
      this.loadData()
      this.$message.success('删除成功');
    },
    formatAddress(address) {
      let parts = [address.address, address.address1, address.street, address.city, address.province, address.countyCode, address.zip];
      return parts.filter(p => p).join(', ');
    }
  }
}
</script>

<style scoped>
.address-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}
.table-container {
  flex-grow: 1;
  overflow-y: auto;
}
.pagination-container {
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}
</style>

