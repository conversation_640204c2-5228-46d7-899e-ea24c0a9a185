<template>
  <div class="box rounded-3" style="height: 100%;" v-loading="loading">
    <component :is="currentComponentInstance" v-if="isDataReady" />
    <!-- <component :is="currentComponentInstance" /> -->
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import ModelGroupTabs from "./ModelGroupTabs.vue";
import ModelElemList from "./ModelElemList.vue";
import ModelElem from "./ModelElem.vue";
import SizeColumn from "./SizeColumn.vue";
import BodyList from "./BodyList.vue";
import Address from "./Address.vue";
import C<PERSON><PERSON>erson from "./ClientPerson.vue";
import SizeColumnAndBody from "./SizeColumnAndBody.vue";
import useCustomMadeStore from "@/store/custommade.js";

export default defineComponent({
  name: "ModelGroupMain",
  components: {
    ModelGroupTabs,
    ModelE<PERSON>List,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Address,
    <PERSON>ze<PERSON><PERSON>umnAndBody,
    <PERSON>lient<PERSON>erson,
  },
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    isDataReady: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    const activeComponentsStore = useCustomMadeStore();
    return {
      activeComponentsStore: activeComponentsStore,
      currentComponent: "ModelGroupTabs",
    };
  },
  computed: {
    currentComponentInstance() {
      const components: { [key: string]: any } = {
        ModelGroupTabs: ModelGroupTabs,
        ModelElemList: ModelElemList,
        ModelElem: ModelElem,
        SizeColumn: SizeColumn,
        BodyList: BodyList,
        Address: Address,
        SizeColumnAndBody: SizeColumnAndBody,
        ClientPerson: ClientPerson,
      };
      return components[this.currentComponent];
    },
  },
  watch: {
    "activeComponentsStore.activeComponentName": {
      handler(newComponent: string) {
        this.currentComponent = newComponent;
      },
      immediate: true,
    },
  },
});
</script>

<style lang="scss">
.list-enter-active {
  transition: all 0.5s ease-out;
}

.list-leave-active {
  transition: all 0s cubic-bezier(1, 0.5, 0.8, 1);
}

.list-enter-from,
.list-leave-to {
  transform: translateX(500px);
  opacity: 0;
}

/* 确保将离开的元素从布局流中删除
    以便能够正确地计算移动的动画。 */
.list-leave-active {
  position: absolute;
}
</style>
