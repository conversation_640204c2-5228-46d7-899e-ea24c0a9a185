image: docker

stages:
  - build
  - docker
  - deploy

build:
  stage: build
  image: node:18.20.0
  only:
    #    - master
    - tags
  cache:
    paths:
      - node_modules/
  script:
    #- cnpm -v
    - npm config  set registry https://registry.npmmirror.com/
    #- cnpm config  set registry https://registry.npmmirror.com/
    # 判断cnpm是否存在，不存在就安装
    - if ! command -v cnpm &> /dev/null; then npm install cnpm -g --registry=https://registry.npmmirror.com; fi
    #- npm install cnpm -g --registry=https://registry.npmmirror.com
    #- export NODE_OPTIONS=--openssl-legacy-provider
    - cnpm install
    - if [[ "${CI_COMMIT_TAG}" =~ ^p.* ]]; then npm run build:production; fi
    - if [[ "${CI_COMMIT_TAG}" =~ ^t.* ]]; then npm run build:dev; fi
  #    - chmod a+x ./build.sh
  #    - ./build.sh ${CI_COMMIT_TAG}
  tags:
    - sunday
  artifacts:
    paths:
      - dist

docker:
  stage: docker
  only:
    - tags
  script:
    - echo "$(date +"%Y%m%d%H%M%S")" "推送镜像"
    #    - docker info
    - DATE=`date +%Y%m%d`
    - TAG=$CI_COMMIT_TAG
    - echo "$TAG"
    - docker login -u ${DOCKER_USERNAME} -p ${DOCKER_PASSWORD} ${DOCKER_REGISTER}
    - docker pull harbor.xmmtm.com/library/nginx:1.19-alpine
    - docker build -t ${PROJECT_NAME}:$TAG .
    - docker tag ${PROJECT_NAME}:$TAG ${DOCKER_REGISTER}/${PROJECT_NAMESPACE}/${PROJECT_NAME}:$TAG
    - docker push ${DOCKER_REGISTER}/${PROJECT_NAMESPACE}/${PROJECT_NAME}:$TAG
    - echo "$(date +"%Y%m%d%H%M%S")" "删除本地镜像"
    - docker rmi -f ${DOCKER_REGISTER}/${PROJECT_NAMESPACE}/${PROJECT_NAME}:$TAG
    - docker rmi -f ${PROJECT_NAME}:$TAG
  tags:
    - sunday

deploy:
  stage: deploy
  # variables:
  #   GIT_STRATEGY: none # 禁止默认拉取代码
  only:
    - tags
  tags:
    - sunday
  script:
    - echo '部署'
    - chmod a+x ./update.sh
    - ./update.sh  "${PROJECT_NAME}" "${CI_COMMIT_TAG}" "${CI_WeChat_WEBHOOK_URL}" "${CI_COMMIT_TITLE}"
