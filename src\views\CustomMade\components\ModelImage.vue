<template>
  <div style="position: relative; height: 100%;width: 100%;">
    <div class="modelcodename">{{ activeGroup.modelCode }} {{ activeGroup.modelCodeName }}</div>
    <div class="imagedata">
      <div v-for="(item,index) in imagesData" :key="index" style="" class="image-item">
        <img :src="item" class="image multi-content" style="max-height: 100%; max-width: 100%; object-fit: contain;" />
      </div>
    </div>

  </div>
</template>

<script >
import { mapState } from "pinia";
import useCustomMadeStore from "@/store/custommade.js";
import jacketImage from "../images/jacket.png";
import trousersImage from "../images/trousers.png";
import waistcoatImage from "../images/waistcoat.png";
import image1 from "../images/1.png";
import image2 from "../images/2.png";
import image3 from "../images/3.png";
import image4 from "../images/4.png";
import image5 from "../images/5.png";
import image6 from "../images/6.png";

export default {
  data() {
    return {
      imagesData: [image2, image4, image6],
      imageUrl: jacketImage,
      groupImages: {
        jacket: jacketImage,
        trousers: trousersImage,
        waistcoat: waistcoatImage,
      },
    };
  },
  computed: {
    ...mapState(useCustomMadeStore, {
      activeGroup: (store) => store.activeGroup,
    }),
  },
  methods: {
    setGroupImage(group) {
      this.imageUrl = this.groupImages[group] || this.groupImages.jacket;
    },
  },
  watch: {
    "activeGroup.code": {
      handler(newValue) {
        if (newValue) {
          this.setGroupImage(newValue);
        }
      },
      immediate: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.imagedata {
  background-color: white;

  .image-item {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
  }
}
.modelcodename {
  position: absolute;
  top: 1.5rem;
  right: 10%;
  z-index: 1;
}
</style>
