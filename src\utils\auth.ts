import Cookies from "js-cookie";

export function getCookeies(name: string) {
  const c = Cookies.get(`xmshop-${name}`);
  if (c) {
    try {
      return JSON.parse(c);
    } catch (e) {
      return c;
    }
  }
  return null;
}

export function setCookeies(name: string, value: any): void {
  const cookieOptions = {
    expires: 1,
  };
  const valueToStore =
    typeof value === "object" && value !== null ? JSON.stringify(value) : value;
  Cookies.set(`xmshop-${name}`, valueToStore, cookieOptions);
}

export function removeCookeies(name: string): void {
  Cookies.remove(`xmshop-${name}`);
}
export function qsStringify(obj: any, prefix?: string): string {
  const result: string[] = [];

  function encode(value: any): string {
    return encodeURIComponent(String(value));
  }

  function serialize(value: any, key: string): void {
    if (Array.isArray(value)) {
      // 数组的处理：key[]=value1&key[]=value2
      value.forEach((item) => {
        serialize(item, `${key}[]`);
      });
    } else if (typeof value === "object" && value !== null) {
      // 嵌套对象的处理：key.subkey=value
      Object.keys(value).forEach((subKey) => {
        serialize(value[subKey], `${key}${key ? "." : ""}${subKey}`);
      });
    } else {
      // 基本类型：直接序列化
      result.push(`${encode(key)}=${encode(value)}`);
    }
  }

  if (typeof obj === "object" && obj !== null) {
    Object.keys(obj).forEach((key) => {
      serialize(obj[key], prefix ? `${prefix}[${key}]` : key);
    });
  } else {
    // 如果传入的是基本类型，直接序列化
    result.push(`${encode(prefix || "")}=${encode(obj)}`);
  }

  return result.join("&");
}
