declare module "@/api" {
  const api: {
    ActionRequest(
      apiurl: string,
      data?: any,
      successmsg?: boolean,
      type?: string,
      baseurl?: string
    ): Promise<any>;
    ActionRequestOther(
      apiurl: string,
      data?: any,
      successmsg?: boolean,
      type?: string,
      baseurl?: string
    ): Promise<any>;
    ActionFileRequest(
      apiurl: string,
      data?: any,
      successmsg?: boolean,
      type?: string,
      baseurl?: string
    ): Promise<any>;
    ActionExcelRequest(
      apiurl: string,
      data?: any,
      successmsg?: boolean,
      type?: string,
      baseurl?: string
    ): Promise<any>;
    ActionAndTokenRequest(
      apiurl: string,
      data?: any,
      token?: string,
      successmsg?: boolean,
      type?: string,
      baseurl?: string
    ): Promise<any>;
    [key: string]: any;
  };
  export default api;
}
